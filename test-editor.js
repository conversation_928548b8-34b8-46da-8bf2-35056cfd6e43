// Gateway Config Editor 测试脚本
const fs = require('fs');
const path = require('path');

console.log('🧪 Gateway Config Editor 测试 (v2.0 - 安全版本)');
console.log('================================================');

// 测试1: 检查必要文件是否存在
console.log('\n📁 检查文件完整性...');
const requiredFiles = [
    'config-editor.html',
    'config-editor.css',
    'config-editor.js',
    'config-api.js',
    'login.html',
    'package.json',
    '.env'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - 文件缺失`);
        allFilesExist = false;
    }
});

// 测试2: 检查配置文件
console.log('\n📄 检查配置文件...');
const configPaths = [
    './conf.json',
    '/root/test/gateway/conf.json'
];

let configFound = false;
configPaths.forEach(configPath => {
    if (fs.existsSync(configPath)) {
        console.log(`✅ 找到配置文件: ${configPath}`);
        configFound = true;
        
        // 验证JSON格式
        try {
            const content = fs.readFileSync(configPath, 'utf8');
            const config = JSON.parse(content);
            console.log(`   - JSON格式正确`);
            console.log(`   - 包含 ${Object.keys(config.models || {}).length} 个模型`);
            console.log(`   - 包含 ${Object.keys(config.configurations || {}).length} 个配置`);
            console.log(`   - 包含 ${Object.keys(config.providers || {}).length} 个提供商`);
            console.log(`   - 网关路径: ${(config.gateway_paths || []).join(', ')}`);
        } catch (error) {
            console.log(`   ❌ JSON格式错误: ${error.message}`);
        }
    }
});

if (!configFound) {
    console.log('⚠️  未找到配置文件，编辑器将使用示例数据');
}

// 测试3: 检查依赖
console.log('\n📦 检查依赖包...');
if (fs.existsSync('package.json')) {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = packageJson.dependencies || {};
    
    Object.keys(dependencies).forEach(dep => {
        const depPath = path.join('node_modules', dep);
        if (fs.existsSync(depPath)) {
            console.log(`✅ ${dep}`);
        } else {
            console.log(`❌ ${dep} - 需要运行 npm install`);
        }
    });
} else {
    console.log('❌ package.json 不存在');
}

// 测试4: 检查端口
console.log('\n🌐 检查端口占用...');
const net = require('net');
const port = 3474;

const server = net.createServer();
server.listen(port, () => {
    console.log(`✅ 端口 ${port} 可用`);
    server.close();
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`⚠️  端口 ${port} 已被占用`);
    } else {
        console.log(`❌ 端口检查失败: ${err.message}`);
    }
});

// 测试5: 生成测试配置
console.log('\n🔧 生成测试配置...');
if (!fs.existsSync('./conf.json')) {
    const testConfig = {
        "gateway_paths": ["gateway", "test", "api"],
        "configurations": {
            "test-config-1": {
                "strategy": { "mode": "loadbalance" },
                "targets": ["provider1", "provider2"]
            },
            "test-config-2": {
                "strategy": { "mode": "fallback" },
                "targets": ["provider1", "provider2", "provider3"],
                "retry": { "attempts": 3 }
            },
            "simple-config": "provider1"
        },
        "models": {
            "gpt-4o": {
                "config-name": "test-config-1",
                "use-in": ["gateway", "api"],
                "type": ["chat"]
            },
            "gpt-4o-mini": {
                "config-name": "test-config-2", 
                "use-in": ["test"],
                "type": ["chat"]
            },
            "dall-e-3": {
                "config-name": "simple-config",
                "use-in": ["gateway"],
                "type": ["image"]
            }
        },
        "modelsmap": {
            "gpt-4": "gpt-4o",
            "gpt-3.5-turbo": "gpt-4o-mini"
        },
        "providers": {
            "provider1": {
                "provider": "openai",
                "api_key": "sk-test-key-1"
            },
            "provider2": {
                "provider": "anthropic", 
                "api_key": "sk-test-key-2"
            },
            "provider3": {
                "provider": "azure-openai",
                "api_key": "test-key-3",
                "custom_host": "https://test.openai.azure.com"
            }
        }
    };
    
    fs.writeFileSync('./conf.json', JSON.stringify(testConfig, null, 2));
    console.log('✅ 已生成测试配置文件: ./conf.json');
} else {
    console.log('ℹ️  配置文件已存在，跳过生成');
}

// 测试总结
console.log('\n📊 测试总结');
console.log('================================');
if (allFilesExist) {
    console.log('✅ 所有必要文件完整');
} else {
    console.log('❌ 部分文件缺失，请检查');
}

console.log('\n🚀 启动建议:');
console.log('1. 检查并配置 .env 文件中的密码');
console.log('2. 运行 npm install 安装依赖');
console.log('3. 运行 npm start 启动服务');
console.log('4. 访问 http://localhost:3474/login.html 登录');
console.log('5. 访问 http://localhost:3474/demo.html 查看演示');

console.log('\n🔐 安全提示:');
console.log('- 默认密码: admin123 (请在 .env 文件中修改)');
console.log('- JWT密钥: 请在生产环境中使用强密钥');
console.log('- 会话超时: 默认60分钟');

console.log('\n💡 使用提示:');
console.log('- 使用 start-editor.sh (Linux/Mac) 或 start-editor.bat (Windows) 快速启动');
console.log('- 编辑器会自动检测配置文件位置');
console.log('- 支持复杂的多层嵌套配置结构');
console.log('- 所有修改都会自动备份');

console.log('\n测试完成! 🎉');
