# 附加参数功能快速指南

## 🚀 快速开始

### 1. 启动编辑器
```bash
npm start
```

### 2. 登录系统
访问 `http://localhost:3474/login.html`，使用密码 `admin123`

### 3. 查看附加参数
1. 进入"配置策略"部分
2. 展开包含附加参数的配置项
3. 查看"附加参数"部分

## 🎯 核心功能演示

### 可视化参数编辑器
- **分类显示**: 按覆盖、添加、移除参数分组
- **直接编辑**: 点击输入框直接修改参数
- **类型识别**: 自动识别字符串、数字、布尔值、JSON

### 参数类型说明

#### 🔧 覆盖参数 (override_params)
用于覆盖请求中的现有参数：
```json
{
  "model": "gpt-4-turbo",
  "max_tokens": 4000,
  "temperature": 0.7
}
```

#### ➕ 添加参数 (add_params)
用于向请求中添加额外参数：
```json
{
  "custom_id": "request-123",
  "metadata": {
    "source": "web-app"
  }
}
```

#### ➖ 移除参数 (remove_params)
用于从请求中移除指定参数：
```json
["user", "stream", "logit_bias"]
```

## 📝 操作步骤

### 编辑现有参数
1. 找到要编辑的参数字段
2. 点击参数名或参数值输入框
3. 直接修改内容
4. 点击其他地方保存

### 添加新参数
1. 点击"添加参数"按钮
2. 选择参数类型（覆盖/添加/移除）
3. 填写参数名和值
4. 点击"保存"

### 删除参数
- **删除单个参数**: 点击参数右侧的 ❌ 按钮
- **删除参数组**: 点击参数组标题右侧的 🗑️ 按钮

### 高级编辑
1. 点击"高级编辑"按钮
2. 在JSON编辑器中编辑完整配置
3. 查看内置的参数说明和示例
4. 点击"保存"应用更改

## 💡 实用示例

### 模型参数优化
```json
{
  "override_params": {
    "model": "gpt-4-turbo",
    "max_tokens": 4000,
    "temperature": 0.1,
    "top_p": 0.9
  }
}
```

### 安全设置配置
```json
{
  "add_params": {
    "safety_settings": [
      {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
      }
    ]
  }
}
```

### 清理不需要的参数
```json
{
  "remove_params": [
    "stream",
    "echo",
    "logit_bias"
  ]
}
```

## 🎨 界面提示

### 图标含义
- **⚙️**: 覆盖参数组
- **➕**: 添加参数组  
- **➖**: 移除参数组
- **✏️**: 高级编辑模式
- **🗑️**: 删除操作

### 颜色编码
- **蓝色边框**: 参数组容器
- **灰色背景**: 普通参数字段
- **蓝色背景**: 悬停状态

## ⚠️ 注意事项

### 参数值格式
- **字符串**: 直接输入文本
- **数字**: 输入数字（自动识别）
- **布尔值**: 输入 `true` 或 `false`
- **JSON**: 输入完整的JSON格式

### 常见错误
- ❌ JSON格式错误
- ❌ 参数名为空
- ❌ 无效的参数值

## 🧪 测试功能

运行测试脚本验证功能：
```bash
node test-params-editing.js
```

## 📚 更多信息

- 详细说明: `附加参数编辑功能说明.md`
- 项目总结: `项目总结.md`
- 使用手册: `README_配置编辑器使用说明.md`

## 🎉 功能优势

1. **可视化操作**: 无需手写JSON
2. **类型安全**: 自动识别参数类型
3. **实时预览**: 修改后立即显示
4. **错误提示**: 及时发现配置问题
5. **灵活配置**: 支持复杂参数结构

现在您可以轻松管理复杂的API参数配置！
