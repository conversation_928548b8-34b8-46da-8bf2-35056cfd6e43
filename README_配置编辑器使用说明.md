# Gateway 配置编辑器

一个现代化的、可视化的网关配置文件编辑器，支持PC和移动端，提供直观的界面来管理复杂的网关配置。

## 🔐 安全特性

### 登录认证
- **密码保护**: 使用环境变量配置的密码保护访问
- **JWT认证**: 基于JSON Web Token的会话管理
- **自动登出**: 会话超时自动登出
- **安全配置**: 密码和密钥存储在环境变量中

## 功能特性

### 🎯 核心功能
- **可视化编辑**: 直观的界面编辑复杂的JSON配置
- **多层嵌套支持**: 完整支持复杂的嵌套配置结构
- **响应式设计**: 完美适配PC和移动设备
- **实时验证**: 配置修改时实时验证格式和引用
- **拖拽排序**: 支持fallback模式下的目标拖拽排序
- **智能提示**: 自动补全和候选项提示

### 📱 界面特性
- **现代化UI**: 采用现代设计语言，界面简洁美观
- **分类管理**: 按功能模块分类管理配置项
- **搜索过滤**: 快速搜索和过滤配置项
- **批量操作**: 支持批量编辑和操作

### 🔧 配置管理
- **网关路径**: 管理可用的网关路径
- **配置策略**: 负载均衡和故障转移策略配置
- **模型配置**: 模型使用范围和类型管理
- **模型映射**: 模型名称映射关系
- **提供商配置**: API提供商连接信息

## 安装和使用

### 1. 配置环境变量
复制并编辑 `.env` 文件：
```bash
# 设置登录密码
LOGIN_PASSWORD=your-secure-password

# 设置JWT密钥
JWT_SECRET=your-super-secret-jwt-key

# 其他配置...
```

### 2. 安装依赖
```bash
npm install
```

### 3. 启动服务
```bash
npm start
```

### 4. 访问编辑器
1. 打开浏览器访问: `http://localhost:3474/login.html`
2. 输入配置的密码登录
3. 成功登录后自动跳转到编辑器界面

## 界面说明

### 主要区域
1. **头部工具栏**: 加载和保存配置按钮
2. **侧边导航**: 功能模块切换
3. **内容区域**: 具体配置编辑界面
4. **模态框**: 详细编辑弹窗

### 功能模块

#### 1. 网关路径 (Gateway Paths)
- 配置可用的网关路径
- 支持任意字母数字组合，区分大小写
- 路径变更会自动更新模型的使用范围选项

#### 2. 配置策略 (Configurations)
- **简单配置**: 直接引用提供商
- **复杂配置**: 包含策略、重试、目标等
- **策略模式**:
  - `loadbalance`: 负载均衡，目标顺序无关
  - `fallback`: 故障转移，支持拖拽排序

#### 3. 模型配置 (Models)
- **配置名称**: 关联的配置策略
- **使用范围**: 点选式编辑，来源于网关路径
- **类型标签**: chat、image、embedding、voice、stop

#### 4. 模型映射 (Models Map)
- 配置模型名称的映射关系
- 支持别名和重定向

#### 5. 提供商配置 (Providers)
- API连接信息配置
- 支持多种提供商类型
- 安全的密钥管理

## 特殊功能

### 拖拽排序
当配置策略的模式为 `fallback` 时，目标列表支持拖拽排序：
1. 鼠标悬停在目标项上显示拖拽手柄
2. 拖拽调整顺序
3. 实时保存排序结果

### 智能标签选择
模型的"使用范围"编辑：
1. 自动显示所有可用的网关路径
2. 点击标签切换选中状态
3. 选中的路径高亮显示
4. 路径变更时自动更新选项

### 实时搜索
在模型和提供商列表中：
1. 输入关键词实时过滤
2. 支持模糊匹配
3. 高亮匹配结果

## 数据安全

### 自动备份
- 每次保存前自动创建备份
- 备份文件带时间戳
- 支持查看和恢复历史备份

### 配置验证
- 保存前验证JSON格式
- 检查引用完整性
- 提示错误和警告信息

### 错误处理
- 友好的错误提示
- 操作失败时保留用户输入
- 网络异常时的降级处理

## 技术特性

### 前端技术
- **原生JavaScript**: 无框架依赖，轻量高效
- **CSS Grid/Flexbox**: 现代布局技术
- **CSS前缀**: 所有样式使用 `gw-` 前缀避免冲突
- **响应式设计**: 移动端优先的设计理念

### 后端API
- **Express.js**: 轻量级Web框架
- **文件操作**: 直接读写配置文件
- **CORS支持**: 跨域请求支持
- **错误处理**: 完善的错误处理机制

### 兼容性
- **现代浏览器**: Chrome 60+, Firefox 60+, Safari 12+
- **移动设备**: iOS Safari, Android Chrome
- **响应式断点**: 768px 移动端适配

## 配置文件结构

```json
{
  "gateway_paths": ["gateway", "test"],
  "configurations": {
    "config-name": "provider-name" | {
      "strategy": { "mode": "loadbalance|fallback" },
      "retry": { "attempts": 3 },
      "targets": ["provider1", "provider2"]
    }
  },
  "models": {
    "model-name": {
      "config-name": "config-reference",
      "use-in": ["path1", "path2"],
      "type": ["chat", "image"]
    }
  },
  "modelsmap": {
    "alias": "real-model-name"
  },
  "providers": {
    "provider-name": {
      "provider": "openai",
      "api_key": "sk-...",
      "custom_host": "https://api.example.com"
    }
  }
}
```

## 注意事项

1. **配置文件路径**: 默认为 `/root/test/gateway/conf.json`
2. **端口冲突**: 编辑器使用端口 3474，确保端口未被占用
3. **权限要求**: 需要配置文件的读写权限
4. **备份管理**: 定期清理过期的备份文件
5. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验

## 故障排除

### 常见问题
1. **配置加载失败**: 检查文件路径和权限
2. **保存失败**: 确认磁盘空间和写入权限
3. **界面显示异常**: 清除浏览器缓存
4. **拖拽不工作**: 确认浏览器支持HTML5拖拽

### 日志查看
服务器日志会显示详细的操作信息和错误详情，有助于问题诊断。

## 开发和扩展

### 自定义样式
所有CSS类都使用 `gw-` 前缀，可以安全地集成到其他系统中。

### API扩展
后端API设计简洁，易于扩展新功能。

### 功能定制
前端组件化设计，便于添加新的配置类型和编辑器。
