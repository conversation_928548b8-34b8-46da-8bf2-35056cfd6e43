# 附加参数编辑功能说明

## 🎯 功能概述

配置编辑器现在提供了强大的附加参数编辑功能，支持可视化编辑、添加和管理复杂的参数配置，无需手动编写JSON。

## 📋 支持的参数类型

### 1. 覆盖参数 (override_params)
用于覆盖请求中的现有参数，常用于：
- 指定特定的模型名称
- 设置最大token数
- 调整温度参数
- 配置其他模型参数

### 2. 添加参数 (add_params)
用于向请求中添加额外的参数，常用于：
- 添加自定义头部
- 设置特殊标识
- 添加调试信息

### 3. 移除参数 (remove_params)
用于从请求中移除指定的参数，常用于：
- 移除不需要的参数
- 清理敏感信息
- 简化请求结构

## 🔧 编辑功能

### 可视化参数编辑器
- ✅ **分类显示**: 按参数类型分组显示
- ✅ **键值编辑**: 直接编辑参数名和值
- ✅ **类型识别**: 自动识别字符串、数字、布尔值、JSON
- ✅ **实时更新**: 修改后立即生效

### 参数管理操作
- ✅ **添加参数**: 支持添加各种类型的参数
- ✅ **删除参数**: 删除单个参数或整个参数组
- ✅ **修改参数**: 修改参数名称和值
- ✅ **高级编辑**: JSON格式的高级编辑模式

## 📖 使用指南

### 查看附加参数
1. 进入"配置策略"部分
2. 展开包含附加参数的配置项
3. 查看"附加参数"部分的可视化显示

### 编辑现有参数
1. 找到要编辑的参数字段
2. 直接点击输入框进行修改
3. 支持的值类型：
   - **字符串**: 直接输入文本
   - **数字**: 输入数字（自动识别整数/小数）
   - **布尔值**: 输入 `true` 或 `false`
   - **JSON**: 输入JSON格式的复杂数据

### 添加新参数
1. 点击"添加参数"按钮
2. 选择参数类型：
   - **覆盖参数**: 覆盖现有参数
   - **添加参数**: 添加新参数
   - **移除参数**: 指定要移除的参数名
3. 填写参数信息：
   - **参数名**: 参数的键名
   - **参数值**: 参数的值（支持多种类型）
4. 点击"保存"确认添加

### 删除参数
- **删除单个参数**: 点击参数右侧的删除按钮
- **删除参数组**: 点击参数组标题右侧的删除按钮

### 高级编辑模式
1. 点击"高级编辑"按钮
2. 在JSON编辑器中直接编辑完整的参数配置
3. 查看内置的参数说明和示例
4. 点击"保存"应用更改

## 💡 常用参数示例

### 模型参数覆盖
```json
{
  "override_params": {
    "model": "gpt-4-turbo",
    "max_tokens": 4000,
    "temperature": 0.7,
    "top_p": 0.9,
    "frequency_penalty": 0.1,
    "presence_penalty": 0.1
  }
}
```

### 添加自定义参数
```json
{
  "add_params": {
    "custom_id": "my-request-123",
    "metadata": {
      "source": "web-app",
      "version": "1.0"
    }
  }
}
```

### 移除敏感参数
```json
{
  "remove_params": [
    "user",
    "stream",
    "logit_bias"
  ]
}
```

### 复合配置示例
```json
{
  "override_params": {
    "model": "gemini-1.5-flash",
    "max_tokens": 2000
  },
  "add_params": {
    "safety_settings": {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_MEDIUM_AND_ABOVE"
    }
  },
  "remove_params": [
    "stream"
  ]
}
```

## 🎨 界面元素说明

### 图标含义
- **⚙️ 覆盖参数**: 用于覆盖现有参数
- **➕ 添加参数**: 用于添加新参数
- **➖ 移除参数**: 用于移除指定参数
- **✏️ 编辑按钮**: 进入高级编辑模式
- **➕ 添加参数**: 添加新的参数字段
- **🗑️ 删除按钮**: 删除参数或参数组

### 颜色编码
- **蓝色边框**: 参数组容器
- **灰色背景**: 普通参数字段
- **蓝色背景**: 悬停状态
- **红色按钮**: 删除操作

## ⚠️ 注意事项

### 参数值格式
1. **字符串**: 直接输入，如 `"gpt-4"`
2. **数字**: 输入数字，如 `4000` 或 `0.7`
3. **布尔值**: 输入 `true` 或 `false`
4. **JSON对象**: 输入完整的JSON，如 `{"key": "value"}`
5. **数组**: 输入JSON数组，如 `["item1", "item2"]`

### 参数名称规范
- 使用有效的参数名称
- 避免使用特殊字符
- 遵循API文档的参数规范

### 最佳实践
1. **测试验证**: 修改参数后测试API调用
2. **备份配置**: 重要修改前备份配置
3. **渐进修改**: 逐步修改和测试参数
4. **文档参考**: 参考API文档了解参数含义

## 🔍 故障排除

### 常见问题
1. **JSON格式错误**: 检查JSON语法是否正确
2. **参数不生效**: 确认参数名称是否正确
3. **类型错误**: 检查参数值类型是否匹配
4. **保存失败**: 检查网络连接和权限

### 错误提示
- **"JSON格式错误"**: 参数值不是有效的JSON格式
- **"参数名不能为空"**: 必须填写参数名称
- **"保存失败"**: 网络错误或权限问题

## 🚀 高级用法

### 动态参数配置
根据不同的使用场景配置不同的参数：

```json
{
  "override_params": {
    "model": "gpt-4-turbo",
    "max_tokens": 4000,
    "temperature": 0.1
  }
}
```

### 多层参数覆盖
在嵌套配置中，每个层级都可以有自己的参数配置，实现精细化控制。

### 条件参数移除
使用 `remove_params` 可以根据需要移除不必要的参数，优化API调用。

## 🎉 功能优势

1. **可视化操作**: 无需手写JSON，通过界面操作
2. **类型安全**: 自动识别和转换参数类型
3. **实时预览**: 修改后立即看到效果
4. **灵活配置**: 支持各种复杂的参数配置
5. **错误提示**: 及时发现和修正配置错误

现在您可以轻松管理复杂的附加参数配置，无需担心JSON格式问题！
