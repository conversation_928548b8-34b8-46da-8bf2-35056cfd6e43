{"name": "gateway-config-editor", "version": "1.0.0", "description": "可视化网关配置编辑器", "main": "config-api.js", "scripts": {"start": "node config-api.js", "dev": "nodemon config-api.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["gateway", "config", "editor", "visual"], "author": "Gateway Config Team", "license": "MIT"}