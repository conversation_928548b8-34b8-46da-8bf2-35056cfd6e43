// Gateway Config Editor JavaScript
class GatewayConfigEditor {
    constructor() {
        this.config = {};
        this.currentSection = 'gateway-paths';
        this.authToken = localStorage.getItem('gw_auth_token');
        this.init();
    }

    async init() {
        await this.checkAuth();
        this.bindEvents();
        this.loadConfig();
    }

    async checkAuth() {
        // 检查是否启用认证
        try {
            const response = await fetch('/api/config');
            // 如果能直接访问API，说明认证被禁用
            if (response.status !== 401) {
                return; // 认证被禁用，继续执行
            }
        } catch (error) {
            // 网络错误，继续检查token
        }

        if (!this.authToken) {
            window.location.href = '/login.html';
            return;
        }

        // 验证token有效性
        await this.verifyAuth();
    }

    async verifyAuth() {
        try {
            const response = await fetch('/api/verify-auth', {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            if (!response.ok) {
                localStorage.removeItem('gw_auth_token');
                window.location.href = '/login.html';
                return;
            }
        } catch (error) {
            console.error('Auth verification failed:', error);
            localStorage.removeItem('gw_auth_token');
            window.location.href = '/login.html';
        }
    }

    getAuthHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };

        if (this.authToken) {
            headers['Authorization'] = `Bearer ${this.authToken}`;
        }

        return headers;
    }

    bindEvents() {
        // 导航事件
        document.querySelectorAll('.gw-nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.switchSection(section);
            });
        });

        // 按钮事件
        document.getElementById('gw-load-btn').addEventListener('click', () => this.loadConfig());
        document.getElementById('gw-save-btn').addEventListener('click', () => this.saveConfig());
        document.getElementById('gw-logout-btn').addEventListener('click', () => this.logout());
        document.getElementById('gw-add-path-btn').addEventListener('click', () => this.addPath());
        document.getElementById('gw-add-config-btn').addEventListener('click', () => this.addConfiguration());
        document.getElementById('gw-add-model-btn').addEventListener('click', () => this.addModel());
        document.getElementById('gw-add-mapping-btn').addEventListener('click', () => this.addMapping());
        document.getElementById('gw-add-provider-btn').addEventListener('click', () => this.addProvider());

        // 模态框事件
        document.getElementById('gw-modal-close').addEventListener('click', () => this.closeModal());
        document.getElementById('gw-modal-cancel').addEventListener('click', () => this.closeModal());
        document.getElementById('gw-modal-save').addEventListener('click', () => this.saveModalData());

        // 搜索事件
        document.getElementById('gw-model-search').addEventListener('input', (e) => {
            this.filterModels(e.target.value);
        });
        document.getElementById('gw-provider-search').addEventListener('input', (e) => {
            this.filterProviders(e.target.value);
        });

        // 点击模态框背景关闭
        document.getElementById('gw-modal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeModal();
            }
        });
    }

    switchSection(section) {
        console.log('📑 切换到页面:', section);

        // 更新导航状态
        document.querySelectorAll('.gw-nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.gw-section').forEach(sec => {
            sec.classList.remove('active');
        });
        document.getElementById(section).classList.add('active');

        this.currentSection = section;

        // 如果是配置页面，确保配置已渲染
        if (section === 'configurations') {
            console.log('🔧 重新渲染配置列表');
            setTimeout(() => {
                this.renderConfigurations();
            }, 50);
        }

        console.log('✅ 页面切换完成');
    }

    async loadConfig() {
        this.showLoading(true);
        try {
            const headers = this.authToken ? this.getAuthHeaders() : {};
            const response = await fetch('/api/config', { headers });

            if (response.status === 401) {
                // 认证失败，跳转到登录页面
                localStorage.removeItem('gw_auth_token');
                window.location.href = '/login.html';
                return;
            }

            if (!response.ok) {
                throw new Error('Failed to load configuration');
            }

            this.config = await response.json();
            this.renderAll();
            this.showNotification('配置加载成功', 'success');
        } catch (error) {
            console.error('Error loading config:', error);
            this.showNotification('配置加载失败: ' + error.message, 'error');
            // 使用示例数据进行演示
            this.loadSampleConfig();
        } finally {
            this.showLoading(false);
        }
    }

    loadSampleConfig() {
        // 加载示例配置用于演示
        this.config = {
            gateway_paths: ['gateway', 'test'],
            configurations: {
                'common-config-1': {
                    strategy: { mode: 'fallback' },
                    targets: ['provider1', 'provider2']
                }
            },
            models: {
                'gpt-4o': {
                    'config-name': 'common-config-1',
                    'use-in': ['gateway'],
                    type: ['chat']
                }
            },
            modelsmap: {
                'gpt-4': 'gpt-4o'
            },
            providers: {
                'provider1': {
                    api_key: 'sk-xxx',
                    provider: 'openai'
                }
            }
        };
        this.renderAll();
    }

    async saveConfig() {
        this.showLoading(true);
        try {
            const response = await fetch('/api/config', {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify(this.config)
            });

            if (response.status === 401) {
                // 认证失败，跳转到登录页面
                localStorage.removeItem('gw_auth_token');
                window.location.href = '/login.html';
                return;
            }

            if (!response.ok) {
                throw new Error('Failed to save configuration');
            }

            this.showNotification('配置保存成功', 'success');
        } catch (error) {
            console.error('Error saving config:', error);
            this.showNotification('配置保存失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async logout() {
        try {
            await fetch('/api/logout', {
                method: 'POST',
                headers: this.getAuthHeaders()
            });
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            localStorage.removeItem('gw_auth_token');
            window.location.href = '/login.html';
        }
    }

    renderAll() {
        this.renderGatewayPaths();
        this.renderConfigurations();
        this.renderModels();
        this.renderModelsMap();
        this.renderProviders();

        // 延迟初始化拖拽功能，确保DOM已渲染
        setTimeout(() => {
            this.initAllSortables();
        }, 100);
    }

    renderGatewayPaths() {
        const container = document.getElementById('gw-paths-container');
        const paths = this.config.gateway_paths || [];
        
        container.innerHTML = paths.map((path, index) => `
            <div class="gw-path-item">
                <input type="text" value="${path}" 
                       onchange="editor.updatePath(${index}, this.value)">
                <button class="gw-btn gw-btn-small gw-btn-danger" 
                        onclick="editor.removePath(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    renderConfigurations() {
        const container = document.getElementById('gw-configs-container');
        const configs = this.config.configurations || {};
        
        container.innerHTML = Object.entries(configs).map(([key, config]) => {
            const isSimple = typeof config === 'string';
            return `
                <div class="gw-config-item" data-config="${key}">
                    <div class="gw-config-header" onclick="this.nextElementSibling.classList.toggle('expanded')">
                        <div class="gw-config-title">
                            <i class="fas fa-chevron-right"></i>
                            ${key}
                            ${isSimple ? `<span class="gw-item-subtitle">→ ${config}</span>` : ''}
                        </div>
                        <div class="gw-item-actions">
                            <button class="gw-btn gw-btn-small gw-btn-secondary"
                                    onclick="event.stopPropagation(); editor.editConfiguration('${key}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="gw-btn gw-btn-small gw-btn-danger"
                                    onclick="event.stopPropagation(); editor.removeConfiguration('${key}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="gw-config-body">
                        ${isSimple ?
                            `<p>引用提供商: <strong>${config}</strong></p>` :
                            this.renderConfigDetails(config, key)
                        }
                    </div>
                </div>
            `;
        }).join('');
    }

    renderConfigDetails(config, configName) {
        let html = '';

        if (config.strategy) {
            html += `<p><strong>策略模式:</strong> ${config.strategy.mode}</p>`;
        }

        if (config.retry) {
            html += `<p><strong>重试次数:</strong> ${config.retry.attempts}</p>`;
        }

        if (config.targets) {
            const isFallback = config.strategy && config.strategy.mode === 'fallback';
            html += `<div class="gw-form-group">`;
            html += `<label class="gw-form-label">目标列表 ${isFallback ? '(可拖拽排序)' : ''}</label>`;
            html += this.createNestedTargets(config.targets, configName, isFallback, 0);
            html += `</div>`;
        }

        if (config.base_provider) {
            html += `<p><strong>基础提供商:</strong> ${config.base_provider}</p>`;
        }

        if (config.added_params) {
            html += `<div class="gw-form-group">`;
            html += `<label class="gw-form-label">附加参数</label>`;
            html += `<pre class="gw-json-display">${JSON.stringify(config.added_params, null, 2)}</pre>`;
            html += `</div>`;
        }

        return html || '<p>无详细信息</p>';
    }

    // 处理嵌套的targets结构
    createNestedTargets(targets, configName, isFallback = false, level = 0, path = []) {
        if (!Array.isArray(targets)) return '';

        const indent = level * 20;
        const pathStr = JSON.stringify(path);
        let html = `<div class="gw-nested-targets" style="margin-left: ${indent}px;" data-level="${level}">`;

        // 添加目标按钮
        html += `
            <div class="gw-add-target-container">
                <button class="gw-btn gw-btn-small gw-btn-primary"
                        onclick="editor.showAddTargetModal('${configName}', ${pathStr})">
                    <i class="fas fa-plus"></i> 添加目标
                </button>
            </div>
        `;

        targets.forEach((target, index) => {
            const currentPath = [...path, index];
            const currentPathStr = JSON.stringify(currentPath);

            if (typeof target === 'string') {
                // 简单字符串目标
                html += `
                    <div class="gw-target-item gw-target-simple" draggable="${isFallback}" data-index="${index}">
                        <i class="fas fa-grip-vertical gw-drag-handle" style="display: ${isFallback ? 'inline' : 'none'}"></i>
                        <i class="fas fa-server gw-target-icon"></i>
                        <input type="text" class="gw-target-name-input" value="${target}"
                               onchange="editor.updateSimpleTarget('${configName}', ${currentPathStr}, this.value)"
                               onblur="this.classList.remove('editing')"
                               onfocus="this.classList.add('editing')">
                        <div class="gw-target-actions">
                            <button class="gw-btn gw-btn-small gw-btn-danger"
                                    onclick="editor.removeNestedTarget('${configName}', ${currentPathStr})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            } else if (typeof target === 'object') {
                // 复杂对象目标
                const hasStrategy = target.strategy && target.strategy.mode;
                const hasTargets = target.targets && Array.isArray(target.targets);
                const isNestedFallback = hasStrategy && target.strategy.mode === 'fallback';

                html += `
                    <div class="gw-target-item gw-target-complex" draggable="${isFallback}" data-index="${index}">
                        <div class="gw-target-header">
                            <i class="fas fa-grip-vertical gw-drag-handle" style="display: ${isFallback ? 'inline' : 'none'}"></i>
                            <i class="fas fa-layer-group gw-target-icon"></i>
                            <span class="gw-target-name">
                                ${hasStrategy ? `${target.strategy.mode} 策略` : '复杂配置'}
                                ${hasTargets ? ` (${target.targets.length} 个目标)` : ''}
                            </span>
                            <div class="gw-target-actions">
                                <button class="gw-btn gw-btn-small gw-btn-secondary"
                                        onclick="editor.editNestedTarget('${configName}', ${currentPathStr})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="gw-btn gw-btn-small gw-btn-secondary"
                                        onclick="editor.toggleNestedTarget(this)">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <button class="gw-btn gw-btn-small gw-btn-danger"
                                        onclick="editor.removeNestedTarget('${configName}', ${currentPathStr})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="gw-target-body" style="display: none;">
                `;

                // 显示和编辑策略信息
                if (hasStrategy) {
                    html += `
                        <div class="gw-form-group">
                            <label class="gw-form-label">策略模式</label>
                            <select class="gw-form-select"
                                    onchange="editor.updateTargetStrategy('${configName}', ${currentPathStr}, this.value)">
                                <option value="loadbalance" ${target.strategy.mode === 'loadbalance' ? 'selected' : ''}>负载均衡</option>
                                <option value="fallback" ${target.strategy.mode === 'fallback' ? 'selected' : ''}>故障转移</option>
                            </select>
                        </div>
                    `;
                }

                // 显示和编辑基础提供商
                if (target.base_provider) {
                    html += `
                        <div class="gw-form-group">
                            <label class="gw-form-label">基础提供商</label>
                            <input type="text" class="gw-form-input" value="${target.base_provider}"
                                   onchange="editor.updateTargetBaseProvider('${configName}', ${currentPathStr}, this.value)">
                        </div>
                    `;
                }

                // 显示和编辑附加参数
                if (target.added_params) {
                    html += `
                        <div class="gw-form-group">
                            <label class="gw-form-label">附加参数</label>
                            <div class="gw-params-editor">
                                ${this.createParamsEditor(target.added_params, configName, currentPathStr)}
                            </div>
                            <div class="gw-params-actions">
                                <button class="gw-btn gw-btn-small gw-btn-secondary"
                                        onclick="editor.showParamsEditor('${configName}', ${currentPathStr})">
                                    <i class="fas fa-edit"></i> 高级编辑
                                </button>
                                <button class="gw-btn gw-btn-small gw-btn-primary"
                                        onclick="editor.addParamField('${configName}', ${currentPathStr})">
                                    <i class="fas fa-plus"></i> 添加参数
                                </button>
                            </div>
                        </div>
                    `;
                }

                // 递归渲染嵌套目标
                if (hasTargets) {
                    html += this.createNestedTargets(target.targets, configName, isNestedFallback, level + 1, currentPath.concat(['targets']));
                }

                html += `
                        </div>
                    </div>
                `;
            }
        });

        html += `</div>`;
        return html;
    }

    // 切换嵌套目标的展开/折叠
    toggleNestedTarget(button) {
        const targetBody = button.closest('.gw-target-complex').querySelector('.gw-target-body');
        const icon = button.querySelector('i');

        if (targetBody.style.display === 'none') {
            targetBody.style.display = 'block';
            icon.className = 'fas fa-chevron-up';
        } else {
            targetBody.style.display = 'none';
            icon.className = 'fas fa-chevron-down';
        }
    }

    // 移除嵌套目标
    removeNestedTarget(configName, path) {
        if (confirm('确定要删除这个目标吗？')) {
            const config = this.config.configurations[configName];
            let parent = config;
            let targets = config.targets;

            // 导航到父级对象
            for (let i = 0; i < path.length - 1; i++) {
                parent = targets[path[i]];
                if (parent.targets) {
                    targets = parent.targets;
                }
            }

            // 删除目标
            const index = path[path.length - 1];
            targets.splice(index, 1);
            this.renderConfigurations();
        }
    }

    // 更新简单目标
    updateSimpleTarget(configName, path, value) {
        const config = this.config.configurations[configName];
        let targets = config.targets;

        // 导航到正确的targets数组
        for (let i = 0; i < path.length - 1; i++) {
            const segment = path[i];
            if (segment === 'targets') {
                continue;
            }
            targets = targets[segment];
            if (targets.targets) {
                targets = targets.targets;
            }
        }

        // 更新值
        const index = path[path.length - 1];
        targets[index] = value;
    }

    // 更新目标策略
    updateTargetStrategy(configName, path, mode) {
        const target = this.getTargetByPath(configName, path);
        if (target && target.strategy) {
            target.strategy.mode = mode;
            this.renderConfigurations();
        }
    }

    // 更新目标基础提供商
    updateTargetBaseProvider(configName, path, provider) {
        const target = this.getTargetByPath(configName, path);
        if (target) {
            target.base_provider = provider;
        }
    }

    // 更新目标参数
    updateTargetParams(configName, path, jsonStr) {
        try {
            const params = JSON.parse(jsonStr);
            const target = this.getTargetByPath(configName, path);
            if (target) {
                target.added_params = params;
            }
        } catch (error) {
            this.showNotification('JSON格式错误: ' + error.message, 'error');
        }
    }

    // 根据路径获取目标对象
    getTargetByPath(configName, path) {
        const config = this.config.configurations[configName];
        let current = config;
        let targets = config.targets;

        for (let i = 0; i < path.length; i++) {
            const segment = path[i];
            if (segment === 'targets') {
                continue;
            }
            current = targets[segment];
            if (current && current.targets) {
                targets = current.targets;
            }
        }

        return current;
    }

    // 编辑嵌套目标
    editNestedTarget(configName, path) {
        const target = this.getTargetByPath(configName, path);
        if (!target) return;

        this.showModal('编辑复杂目标', this.createNestedTargetForm(target), (data) => {
            // 更新目标配置
            Object.assign(target, data);
            this.renderConfigurations();
        });
    }

    // 显示添加目标模态框
    showAddTargetModal(configName, path) {
        this.showModal('添加目标', this.createAddTargetForm(), (data) => {
            this.addNestedTarget(configName, path, data);
        });
    }

    // 添加嵌套目标
    addNestedTarget(configName, path, targetData) {
        const config = this.config.configurations[configName];
        let targets = config.targets;

        // 导航到正确的targets数组
        for (let i = 0; i < path.length; i++) {
            const segment = path[i];
            if (segment === 'targets') {
                continue;
            }
            targets = targets[segment];
            if (targets.targets) {
                targets = targets.targets;
            }
        }

        // 添加新目标
        if (targetData.type === 'simple') {
            targets.push(targetData.value);
        } else {
            const complexTarget = {};

            if (targetData.strategy) {
                complexTarget.strategy = { mode: targetData.strategy };
                complexTarget.targets = [];
            }

            if (targetData.baseProvider) {
                complexTarget.base_provider = targetData.baseProvider;
            }

            if (targetData.params) {
                try {
                    complexTarget.added_params = JSON.parse(targetData.params);
                } catch (error) {
                    this.showNotification('JSON格式错误: ' + error.message, 'error');
                    return;
                }
            }

            targets.push(complexTarget);
        }

        this.renderConfigurations();
    }

    // 创建嵌套目标编辑表单
    createNestedTargetForm(target) {
        const hasStrategy = target.strategy && target.strategy.mode;
        const hasBaseProvider = target.base_provider;
        const hasParams = target.added_params;

        return `
            <div class="gw-form-group">
                <label class="gw-form-label">配置类型</label>
                <div class="gw-checkbox-group">
                    <label class="gw-checkbox-label">
                        <input type="checkbox" id="has-strategy" ${hasStrategy ? 'checked' : ''}
                               onchange="editor.toggleFormSection('strategy-section', this.checked)">
                        包含策略配置
                    </label>
                    <label class="gw-checkbox-label">
                        <input type="checkbox" id="has-base-provider" ${hasBaseProvider ? 'checked' : ''}
                               onchange="editor.toggleFormSection('base-provider-section', this.checked)">
                        包含基础提供商
                    </label>
                    <label class="gw-checkbox-label">
                        <input type="checkbox" id="has-params" ${hasParams ? 'checked' : ''}
                               onchange="editor.toggleFormSection('params-section', this.checked)">
                        包含附加参数
                    </label>
                </div>
            </div>

            <div id="strategy-section" style="display: ${hasStrategy ? 'block' : 'none'}">
                <div class="gw-form-group">
                    <label class="gw-form-label">策略模式</label>
                    <select class="gw-form-select" id="target-strategy">
                        <option value="loadbalance" ${hasStrategy && target.strategy.mode === 'loadbalance' ? 'selected' : ''}>负载均衡</option>
                        <option value="fallback" ${hasStrategy && target.strategy.mode === 'fallback' ? 'selected' : ''}>故障转移</option>
                    </select>
                </div>
            </div>

            <div id="base-provider-section" style="display: ${hasBaseProvider ? 'block' : 'none'}">
                <div class="gw-form-group">
                    <label class="gw-form-label">基础提供商</label>
                    <input type="text" class="gw-form-input" id="target-base-provider"
                           value="${hasBaseProvider ? target.base_provider : ''}"
                           placeholder="例如: openai">
                </div>
            </div>

            <div id="params-section" style="display: ${hasParams ? 'block' : 'none'}">
                <div class="gw-form-group">
                    <label class="gw-form-label">附加参数 (JSON格式)</label>
                    <textarea class="gw-json-editor" id="target-params" rows="8"
                              placeholder='{"override_params": {"model": "gpt-4"}}'>${hasParams ? JSON.stringify(target.added_params, null, 2) : ''}</textarea>
                </div>
            </div>
        `;
    }

    // 创建添加目标表单
    createAddTargetForm() {
        return `
            <div class="gw-form-group">
                <label class="gw-form-label">目标类型</label>
                <select class="gw-form-select" id="target-type" onchange="editor.toggleTargetType(this.value)">
                    <option value="simple">简单目标 (提供商名称)</option>
                    <option value="complex">复杂目标 (包含策略和参数)</option>
                </select>
            </div>

            <div id="simple-target-section">
                <div class="gw-form-group">
                    <label class="gw-form-label">提供商名称</label>
                    <input type="text" class="gw-form-input" id="simple-target-value"
                           placeholder="例如: openai">
                </div>
            </div>

            <div id="complex-target-section" style="display: none;">
                <div class="gw-form-group">
                    <label class="gw-form-label">配置选项</label>
                    <div class="gw-checkbox-group">
                        <label class="gw-checkbox-label">
                            <input type="checkbox" id="add-strategy"
                                   onchange="editor.toggleFormSection('add-strategy-section', this.checked)">
                            添加策略配置
                        </label>
                        <label class="gw-checkbox-label">
                            <input type="checkbox" id="add-base-provider"
                                   onchange="editor.toggleFormSection('add-base-provider-section', this.checked)">
                            添加基础提供商
                        </label>
                        <label class="gw-checkbox-label">
                            <input type="checkbox" id="add-params"
                                   onchange="editor.toggleFormSection('add-params-section', this.checked)">
                            添加附加参数
                        </label>
                    </div>
                </div>

                <div id="add-strategy-section" style="display: none;">
                    <div class="gw-form-group">
                        <label class="gw-form-label">策略模式</label>
                        <select class="gw-form-select" id="add-target-strategy">
                            <option value="loadbalance">负载均衡</option>
                            <option value="fallback">故障转移</option>
                        </select>
                    </div>
                </div>

                <div id="add-base-provider-section" style="display: none;">
                    <div class="gw-form-group">
                        <label class="gw-form-label">基础提供商</label>
                        <input type="text" class="gw-form-input" id="add-target-base-provider"
                               placeholder="例如: openai">
                    </div>
                </div>

                <div id="add-params-section" style="display: none;">
                    <div class="gw-form-group">
                        <label class="gw-form-label">附加参数 (JSON格式)</label>
                        <textarea class="gw-json-editor" id="add-target-params" rows="6"
                                  placeholder='{"override_params": {"model": "gpt-4"}}'></textarea>
                    </div>
                </div>
            </div>
        `;
    }

    // 切换目标类型
    toggleTargetType(type) {
        document.getElementById('simple-target-section').style.display = type === 'simple' ? 'block' : 'none';
        document.getElementById('complex-target-section').style.display = type === 'complex' ? 'block' : 'none';
    }

    // 切换表单区域
    toggleFormSection(sectionId, show) {
        document.getElementById(sectionId).style.display = show ? 'block' : 'none';
    }

    // 创建参数编辑器
    createParamsEditor(params, configName, pathStr) {
        let html = '';

        if (params.override_params) {
            html += `
                <div class="gw-params-section">
                    <h4 class="gw-params-title">
                        <i class="fas fa-cog"></i> 覆盖参数
                        <button class="gw-btn gw-btn-tiny gw-btn-danger"
                                onclick="editor.removeParamsSection('${configName}', ${pathStr}, 'override_params')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </h4>
                    <div class="gw-params-fields">
                        ${this.createParamFields(params.override_params, configName, pathStr, 'override_params')}
                    </div>
                </div>
            `;
        }

        if (params.add_params) {
            html += `
                <div class="gw-params-section">
                    <h4 class="gw-params-title">
                        <i class="fas fa-plus-circle"></i> 添加参数
                        <button class="gw-btn gw-btn-tiny gw-btn-danger"
                                onclick="editor.removeParamsSection('${configName}', ${pathStr}, 'add_params')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </h4>
                    <div class="gw-params-fields">
                        ${this.createParamFields(params.add_params, configName, pathStr, 'add_params')}
                    </div>
                </div>
            `;
        }

        if (params.remove_params) {
            html += `
                <div class="gw-params-section">
                    <h4 class="gw-params-title">
                        <i class="fas fa-minus-circle"></i> 移除参数
                        <button class="gw-btn gw-btn-tiny gw-btn-danger"
                                onclick="editor.removeParamsSection('${configName}', ${pathStr}, 'remove_params')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </h4>
                    <div class="gw-params-list">
                        ${params.remove_params.map((param, index) => `
                            <div class="gw-param-item">
                                <input type="text" class="gw-param-input" value="${param}"
                                       onchange="editor.updateRemoveParam('${configName}', ${pathStr}, ${index}, this.value)">
                                <button class="gw-btn gw-btn-tiny gw-btn-danger"
                                        onclick="editor.removeParam('${configName}', ${pathStr}, 'remove_params', ${index})">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // 添加参数类型选择器
        if (!html) {
            html = '<p class="gw-no-params">暂无附加参数</p>';
        }

        return html;
    }

    // 创建参数字段
    createParamFields(params, configName, pathStr, section) {
        return Object.entries(params).map(([key, value]) => {
            const valueStr = typeof value === 'string' ? value : JSON.stringify(value);
            return `
                <div class="gw-param-field">
                    <div class="gw-param-key">
                        <input type="text" class="gw-param-input" value="${key}"
                               onchange="editor.updateParamKey('${configName}', ${pathStr}, '${section}', '${key}', this.value)">
                    </div>
                    <div class="gw-param-value">
                        <input type="text" class="gw-param-input" value="${valueStr}"
                               onchange="editor.updateParamValue('${configName}', ${pathStr}, '${section}', '${key}', this.value)"
                               placeholder="参数值">
                    </div>
                    <div class="gw-param-actions">
                        <button class="gw-btn gw-btn-tiny gw-btn-danger"
                                onclick="editor.removeParam('${configName}', ${pathStr}, '${section}', '${key}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 显示参数编辑器模态框
    showParamsEditor(configName, path) {
        const target = this.getTargetByPath(configName, path);
        if (!target || !target.added_params) return;

        this.showModal('编辑附加参数', this.createAdvancedParamsForm(target.added_params), (data) => {
            try {
                target.added_params = JSON.parse(data.params);
                this.renderConfigurations();
            } catch (error) {
                this.showNotification('JSON格式错误: ' + error.message, 'error');
            }
        });
    }

    // 创建高级参数编辑表单
    createAdvancedParamsForm(params) {
        return `
            <div class="gw-form-group">
                <label class="gw-form-label">附加参数 (JSON格式)</label>
                <textarea class="gw-json-editor" id="advanced-params" rows="15"
                          placeholder='{"override_params": {"model": "gpt-4", "max_tokens": 4000}}'>${JSON.stringify(params, null, 2)}</textarea>
                <div class="gw-params-help">
                    <h5>常用参数类型：</h5>
                    <ul>
                        <li><strong>override_params</strong>: 覆盖请求参数</li>
                        <li><strong>add_params</strong>: 添加额外参数</li>
                        <li><strong>remove_params</strong>: 移除指定参数（数组格式）</li>
                    </ul>
                    <h5>常用参数示例：</h5>
                    <ul>
                        <li><code>model</code>: 指定模型名称</li>
                        <li><code>max_tokens</code>: 最大token数</li>
                        <li><code>temperature</code>: 温度参数</li>
                        <li><code>top_p</code>: 核采样参数</li>
                    </ul>
                </div>
            </div>
        `;
    }

    // 添加参数字段
    addParamField(configName, path) {
        this.showModal('添加参数', this.createAddParamForm(), (data) => {
            const target = this.getTargetByPath(configName, path);
            if (!target) return;

            if (!target.added_params) {
                target.added_params = {};
            }

            const section = data.section;
            if (!target.added_params[section]) {
                if (section === 'remove_params') {
                    target.added_params[section] = [];
                } else {
                    target.added_params[section] = {};
                }
            }

            if (section === 'remove_params') {
                target.added_params[section].push(data.key);
            } else {
                let value = data.value;
                try {
                    // 尝试解析为JSON
                    value = JSON.parse(data.value);
                } catch (e) {
                    // 如果不是JSON，保持字符串
                }
                target.added_params[section][data.key] = value;
            }

            this.renderConfigurations();
        });
    }

    // 创建添加参数表单
    createAddParamForm() {
        return `
            <div class="gw-form-group">
                <label class="gw-form-label">参数类型</label>
                <select class="gw-form-select" id="param-section" onchange="editor.toggleParamType(this.value)">
                    <option value="override_params">覆盖参数</option>
                    <option value="add_params">添加参数</option>
                    <option value="remove_params">移除参数</option>
                </select>
            </div>

            <div id="key-value-section">
                <div class="gw-form-group">
                    <label class="gw-form-label">参数名</label>
                    <input type="text" class="gw-form-input" id="param-key"
                           placeholder="例如: model, max_tokens, temperature">
                </div>
                <div class="gw-form-group">
                    <label class="gw-form-label">参数值</label>
                    <input type="text" class="gw-form-input" id="param-value"
                           placeholder="例如: gpt-4, 4000, 0.7">
                    <small class="gw-form-help">支持字符串、数字或JSON格式</small>
                </div>
            </div>

            <div id="remove-section" style="display: none;">
                <div class="gw-form-group">
                    <label class="gw-form-label">要移除的参数名</label>
                    <input type="text" class="gw-form-input" id="remove-param-key"
                           placeholder="例如: stream, user">
                </div>
            </div>
        `;
    }

    // 切换参数类型
    toggleParamType(type) {
        document.getElementById('key-value-section').style.display = type === 'remove_params' ? 'none' : 'block';
        document.getElementById('remove-section').style.display = type === 'remove_params' ? 'block' : 'none';
    }

    // 更新参数键
    updateParamKey(configName, path, section, oldKey, newKey) {
        if (oldKey === newKey) return;

        const target = this.getTargetByPath(configName, path);
        if (!target || !target.added_params || !target.added_params[section]) return;

        const value = target.added_params[section][oldKey];
        delete target.added_params[section][oldKey];
        target.added_params[section][newKey] = value;

        this.renderConfigurations();
    }

    // 更新参数值
    updateParamValue(configName, path, section, key, valueStr) {
        const target = this.getTargetByPath(configName, path);
        if (!target || !target.added_params || !target.added_params[section]) return;

        let value = valueStr;
        try {
            // 尝试解析为数字或JSON
            if (/^\d+$/.test(valueStr)) {
                value = parseInt(valueStr);
            } else if (/^\d*\.\d+$/.test(valueStr)) {
                value = parseFloat(valueStr);
            } else if (valueStr === 'true' || valueStr === 'false') {
                value = valueStr === 'true';
            } else if (valueStr.startsWith('{') || valueStr.startsWith('[')) {
                value = JSON.parse(valueStr);
            }
        } catch (e) {
            // 保持字符串格式
        }

        target.added_params[section][key] = value;
    }

    // 更新移除参数
    updateRemoveParam(configName, path, index, value) {
        const target = this.getTargetByPath(configName, path);
        if (!target || !target.added_params || !target.added_params.remove_params) return;

        target.added_params.remove_params[index] = value;
    }

    // 移除参数
    removeParam(configName, path, section, keyOrIndex) {
        const target = this.getTargetByPath(configName, path);
        if (!target || !target.added_params || !target.added_params[section]) return;

        if (section === 'remove_params') {
            target.added_params[section].splice(keyOrIndex, 1);
        } else {
            delete target.added_params[section][keyOrIndex];
        }

        // 如果section为空，删除整个section
        if (section === 'remove_params' && target.added_params[section].length === 0) {
            delete target.added_params[section];
        } else if (section !== 'remove_params' && Object.keys(target.added_params[section]).length === 0) {
            delete target.added_params[section];
        }

        // 如果added_params为空，删除整个added_params
        if (Object.keys(target.added_params).length === 0) {
            delete target.added_params;
        }

        this.renderConfigurations();
    }

    // 移除参数部分
    removeParamsSection(configName, path, section) {
        if (confirm(`确定要删除整个"${this.getParamSectionName(section)}"部分吗？`)) {
            const target = this.getTargetByPath(configName, path);
            if (!target || !target.added_params) return;

            delete target.added_params[section];

            // 如果added_params为空，删除整个added_params
            if (Object.keys(target.added_params).length === 0) {
                delete target.added_params;
            }

            this.renderConfigurations();
        }
    }

    // 获取参数部分名称
    getParamSectionName(section) {
        const names = {
            'override_params': '覆盖参数',
            'add_params': '添加参数',
            'remove_params': '移除参数'
        };
        return names[section] || section;
    }

    renderModels() {
        const container = document.getElementById('gw-models-container');
        const models = this.config.models || {};

        container.innerHTML = Object.entries(models).map(([modelName, modelConfig]) => `
            <div class="gw-model-item" data-model="${modelName}">
                <div class="gw-item-header" onclick="this.nextElementSibling.classList.toggle('expanded')">
                    <div>
                        <div class="gw-item-title">${modelName}</div>
                        <div class="gw-item-subtitle">
                            配置: ${modelConfig['config-name'] ? `
                                <span class="gw-config-link"
                                      data-config-name="${modelConfig['config-name']}"
                                      title="点击跳转到配置">
                                    ${modelConfig['config-name']} <i class="fas fa-external-link-alt"></i>
                                </span>
                            ` : '未设置'} |
                            类型: ${(modelConfig.type || []).join(', ')} |
                            使用范围: ${(modelConfig['use-in'] || []).join(', ')}
                        </div>
                    </div>
                    <div class="gw-item-actions">
                        <button class="gw-btn gw-btn-small gw-btn-secondary"
                                onclick="event.stopPropagation(); editor.editModel('${modelName}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="gw-btn gw-btn-small gw-btn-danger"
                                onclick="event.stopPropagation(); editor.removeModel('${modelName}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="gw-item-body">
                    <div class="gw-form-group">
                        <label class="gw-form-label">配置名称</label>
                        <input type="text" class="gw-form-input" value="${modelConfig['config-name']}" readonly>
                    </div>
                    <div class="gw-form-group">
                        <label class="gw-form-label">使用范围</label>
                        <div class="gw-tag-selector">
                            ${this.renderUseInTags(modelConfig['use-in'] || [])}
                        </div>
                    </div>
                    <div class="gw-form-group">
                        <label class="gw-form-label">类型</label>
                        <div class="gw-tag-selector">
                            ${(modelConfig.type || []).map(type => 
                                `<span class="gw-tag selected">${type}</span>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        // 绑定配置链接点击事件
        this.bindConfigLinks();
    }

    // 绑定配置链接事件
    bindConfigLinks() {
        console.log('🔗 绑定配置链接事件');
        const configLinks = document.querySelectorAll('.gw-config-link[data-config-name]');
        console.log('📋 找到配置链接数量:', configLinks.length);

        configLinks.forEach(link => {
            const configName = link.getAttribute('data-config-name');
            console.log('🔗 绑定配置链接:', configName);

            link.addEventListener('click', (e) => {
                e.stopPropagation();
                console.log('🔗 配置链接被点击:', configName);
                this.jumpToConfiguration(configName);
            });

            // 添加鼠标悬停效果
            link.style.cursor = 'pointer';
        });
    }

    renderUseInTags(selectedPaths) {
        const allPaths = this.config.gateway_paths || [];
        return allPaths.map(path => {
            const isSelected = selectedPaths.includes(path);
            return `<span class="gw-tag ${isSelected ? 'selected' : ''}" 
                          onclick="editor.toggleUseInPath('${path}', this)">${path}</span>`;
        }).join('');
    }

    renderModelsMap() {
        const container = document.getElementById('gw-mappings-container');
        const mappings = this.config.modelsmap || {};

        container.innerHTML = Object.entries(mappings).map(([from, to]) => `
            <div class="gw-mapping-item">
                <div class="gw-mapping-field">
                    <input type="text" value="${from}" placeholder="原模型名"
                           onchange="editor.updateMapping('${from}', this.value, '${to}', 'from')">
                    ${this.config.models && this.config.models[from] ? `
                        <button class="gw-btn gw-btn-tiny gw-btn-secondary gw-jump-btn"
                                onclick="editor.jumpToModel('${from}')"
                                title="跳转到模型">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    ` : ''}
                </div>
                <span class="gw-mapping-arrow">→</span>
                <div class="gw-mapping-field">
                    <input type="text" value="${to}" placeholder="目标模型名"
                           onchange="editor.updateMapping('${from}', '${from}', this.value, 'to')">
                    ${this.config.models && this.config.models[to] ? `
                        <button class="gw-btn gw-btn-tiny gw-btn-secondary gw-jump-btn"
                                onclick="editor.jumpToModel('${to}')"
                                title="跳转到模型">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    ` : ''}
                </div>
                <button class="gw-btn gw-btn-small gw-btn-danger"
                        onclick="editor.removeMapping('${from}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    renderProviders() {
        const container = document.getElementById('gw-providers-container');
        const providers = this.config.providers || {};

        container.innerHTML = Object.entries(providers).map(([providerName, providerConfig]) => `
            <div class="gw-provider-item" data-provider="${providerName}">
                <div class="gw-item-header" onclick="this.nextElementSibling.classList.toggle('expanded')">
                    <div>
                        <div class="gw-item-title">${providerName}</div>
                        <div class="gw-item-subtitle">
                            类型: ${providerConfig.provider || 'unknown'} |
                            ${providerConfig.custom_host ? 'Custom Host' : 'Default Host'}
                        </div>
                    </div>
                    <div class="gw-item-actions">
                        <button class="gw-btn gw-btn-small gw-btn-secondary"
                                onclick="event.stopPropagation(); editor.editProvider('${providerName}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="gw-btn gw-btn-small gw-btn-danger"
                                onclick="event.stopPropagation(); editor.removeProvider('${providerName}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="gw-item-body">
                    <div class="gw-form-group">
                        <label class="gw-form-label">提供商类型</label>
                        <input type="text" class="gw-form-input" value="${providerConfig.provider || ''}" readonly>
                    </div>
                    <div class="gw-form-group">
                        <label class="gw-form-label">API Key</label>
                        <input type="password" class="gw-form-input" value="${providerConfig.api_key || ''}" readonly>
                    </div>
                    ${providerConfig.custom_host ? `
                        <div class="gw-form-group">
                            <label class="gw-form-label">自定义主机</label>
                            <input type="text" class="gw-form-input" value="${providerConfig.custom_host}" readonly>
                        </div>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }

    // 路径管理方法
    addPath() {
        if (!this.config.gateway_paths) {
            this.config.gateway_paths = [];
        }
        this.config.gateway_paths.push('new-path');
        this.renderGatewayPaths();
    }

    updatePath(index, value) {
        if (this.config.gateway_paths && this.config.gateway_paths[index] !== undefined) {
            this.config.gateway_paths[index] = value;
            // 重新渲染模型以更新use-in选项
            this.renderModels();
        }
    }

    removePath(index) {
        if (this.config.gateway_paths && this.config.gateway_paths[index] !== undefined) {
            const pathToRemove = this.config.gateway_paths[index];
            this.config.gateway_paths.splice(index, 1);

            // 从所有模型的use-in中移除这个路径
            Object.values(this.config.models || {}).forEach(model => {
                if (model['use-in']) {
                    model['use-in'] = model['use-in'].filter(path => path !== pathToRemove);
                }
            });

            this.renderGatewayPaths();
            this.renderModels();
        }
    }

    // 模型管理方法
    toggleUseInPath(path, element) {
        const modelItem = element.closest('.gw-model-item');
        const modelName = modelItem.dataset.model;
        const model = this.config.models[modelName];

        if (!model['use-in']) {
            model['use-in'] = [];
        }

        const index = model['use-in'].indexOf(path);
        if (index > -1) {
            model['use-in'].splice(index, 1);
            element.classList.remove('selected');
        } else {
            model['use-in'].push(path);
            element.classList.add('selected');
        }
    }

    addModel() {
        this.showModal('添加模型', this.createModelForm(), (data) => {
            this.config.models[data.name] = {
                'config-name': data.configName,
                'use-in': data.useIn,
                'type': data.type
            };
            this.renderModels();
        });
    }

    editModel(modelName) {
        const model = this.config.models[modelName];
        this.showModal('编辑模型', this.createModelForm(modelName, model), (data) => {
            if (data.name !== modelName) {
                delete this.config.models[modelName];
                this.config.models[data.name] = {
                    'config-name': data.configName,
                    'use-in': data.useIn,
                    'type': data.type
                };
            } else {
                this.config.models[modelName] = {
                    'config-name': data.configName,
                    'use-in': data.useIn,
                    'type': data.type
                };
            }
            this.renderModels();
        });
    }

    removeModel(modelName) {
        if (confirm(`确定要删除模型 "${modelName}" 吗？`)) {
            delete this.config.models[modelName];
            this.renderModels();
        }
    }

    createModelForm(modelName = '', model = {}) {
        const configOptions = Object.keys(this.config.configurations || {});
        const pathOptions = this.config.gateway_paths || [];

        return `
            <div class="gw-form-group">
                <label class="gw-form-label">模型名称</label>
                <input type="text" class="gw-form-input" id="model-name" value="${modelName}"
                       placeholder="例如: gpt-4o">
            </div>
            <div class="gw-form-group">
                <label class="gw-form-label">配置名称</label>
                <div class="gw-config-selector">
                    <select class="gw-form-select" id="model-config" onchange="editor.updateConfigPreview(this.value)">
                        <option value="">选择配置</option>
                        ${configOptions.map(config =>
                            `<option value="${config}" ${model['config-name'] === config ? 'selected' : ''}>${config}</option>`
                        ).join('')}
                    </select>
                    <button type="button" class="gw-btn gw-btn-small gw-btn-secondary gw-jump-btn"
                            id="jump-to-config" style="display: ${model['config-name'] ? 'inline-flex' : 'none'}"
                            title="跳转到配置">
                        <i class="fas fa-external-link-alt"></i> 跳转
                    </button>
                </div>
                <div id="config-preview" class="gw-config-preview">
                    ${model['config-name'] ? this.createConfigPreview(model['config-name']) : ''}
                </div>
            </div>
            <div class="gw-form-group">
                <label class="gw-form-label">使用范围</label>
                <div class="gw-tag-selector">
                    ${pathOptions.map(path => {
                        const isSelected = (model['use-in'] || []).includes(path);
                        return `<span class="gw-tag ${isSelected ? 'selected' : ''}"
                                      onclick="this.classList.toggle('selected')"
                                      data-path="${path}">${path}</span>`;
                    }).join('')}
                </div>
            </div>
            <div class="gw-form-group">
                <label class="gw-form-label">类型</label>
                <div class="gw-tag-selector">
                    ${['chat', 'image', 'embedding', 'voice', 'stop'].map(type => {
                        const isSelected = (model.type || []).includes(type);
                        return `<span class="gw-tag ${isSelected ? 'selected' : ''}"
                                      onclick="this.classList.toggle('selected')"
                                      data-type="${type}">${type}</span>`;
                    }).join('')}
                </div>
            </div>
        `;
    }

    // 映射管理方法
    addMapping() {
        this.showModal('添加模型映射', this.createMappingForm(), (data) => {
            if (!this.config.modelsmap) {
                this.config.modelsmap = {};
            }
            this.config.modelsmap[data.from] = data.to;
            this.renderModelsMap();
        });
    }

    updateMapping(oldFrom, newFrom, newTo, type) {
        if (!this.config.modelsmap) return;

        if (type === 'from' && oldFrom !== newFrom) {
            const value = this.config.modelsmap[oldFrom];
            delete this.config.modelsmap[oldFrom];
            this.config.modelsmap[newFrom] = value;
        } else if (type === 'to') {
            this.config.modelsmap[oldFrom] = newTo;
        }
    }

    removeMapping(from) {
        if (confirm(`确定要删除映射 "${from}" 吗？`)) {
            delete this.config.modelsmap[from];
            this.renderModelsMap();
        }
    }

    createMappingForm(from = '', to = '') {
        return `
            <div class="gw-form-group">
                <label class="gw-form-label">原模型名</label>
                <input type="text" class="gw-form-input" id="mapping-from" value="${from}"
                       placeholder="例如: gpt-4">
            </div>
            <div class="gw-form-group">
                <label class="gw-form-label">目标模型名</label>
                <input type="text" class="gw-form-input" id="mapping-to" value="${to}"
                       placeholder="例如: gpt-4o">
            </div>
        `;
    }

    // 配置管理方法
    addConfiguration() {
        // 初始化空的配置参数
        this.currentConfigParams = {};

        this.showModal('添加配置', this.createConfigForm(), (data) => {
            this.config.configurations[data.name] = data.config;
            this.renderConfigurations();
        });
    }

    editConfiguration(configName) {
        const config = this.config.configurations[configName];
        // 初始化当前配置参数
        this.currentConfigParams = (typeof config === 'object' && config.added_params) ?
            JSON.parse(JSON.stringify(config.added_params)) : {};

        this.showModal('编辑配置', this.createConfigForm(configName, config), (data) => {
            if (data.name !== configName) {
                delete this.config.configurations[configName];
            }
            this.config.configurations[data.name] = data.config;
            this.renderConfigurations();
        });
    }

    removeConfiguration(configName) {
        if (confirm(`确定要删除配置 "${configName}" 吗？`)) {
            delete this.config.configurations[configName];
            this.renderConfigurations();
        }
    }

    createConfigForm(configName = '', config = {}) {
        const isSimple = typeof config === 'string';
        const providers = Object.keys(this.config.providers || {});

        return `
            <div class="gw-form-group">
                <label class="gw-form-label">配置名称</label>
                <input type="text" class="gw-form-input" id="config-name" value="${configName}"
                       placeholder="例如: common-config-1">
            </div>
            <div class="gw-form-group">
                <label class="gw-form-label">配置类型</label>
                <select class="gw-form-select" id="config-type" onchange="editor.toggleConfigType(this.value)">
                    <option value="simple" ${isSimple ? 'selected' : ''}>简单引用</option>
                    <option value="complex" ${!isSimple ? 'selected' : ''}>复杂配置</option>
                </select>
            </div>
            <div id="simple-config" style="display: ${isSimple ? 'block' : 'none'}">
                <div class="gw-form-group">
                    <label class="gw-form-label">引用提供商</label>
                    <select class="gw-form-select" id="provider-ref">
                        <option value="">选择提供商</option>
                        ${providers.map(provider =>
                            `<option value="${provider}" ${config === provider ? 'selected' : ''}>${provider}</option>`
                        ).join('')}
                    </select>
                </div>
            </div>
            <div id="complex-config" style="display: ${!isSimple ? 'block' : 'none'}">
                <div class="gw-form-group">
                    <label class="gw-form-label">基础提供商</label>
                    <input type="text" class="gw-form-input" id="base-provider"
                           value="${config.base_provider || ''}" placeholder="例如: vertex-ai">
                </div>
                <div class="gw-form-group">
                    <label class="gw-form-label">策略模式</label>
                    <select class="gw-form-select" id="strategy-mode">
                        <option value="">无策略</option>
                        <option value="loadbalance" ${config.strategy?.mode === 'loadbalance' ? 'selected' : ''}>负载均衡</option>
                        <option value="fallback" ${config.strategy?.mode === 'fallback' ? 'selected' : ''}>故障转移</option>
                    </select>
                </div>
                <div class="gw-form-group">
                    <label class="gw-form-label">重试次数</label>
                    <input type="number" class="gw-form-input" id="retry-attempts"
                           value="${config.retry?.attempts || ''}" placeholder="可选">
                </div>
                <div class="gw-form-group">
                    <label class="gw-form-label">目标列表 (每行一个)</label>
                    <textarea class="gw-form-input" id="targets-list" rows="4"
                              placeholder="provider1&#10;provider2">${(config.targets || []).join('\n')}</textarea>
                </div>
                <div class="gw-form-group">
                    <label class="gw-form-label">附加参数</label>
                    <div class="gw-config-params-editor">
                        ${config.added_params ? this.createConfigParamsEditor(config.added_params) : '<p class="gw-no-params">暂无附加参数</p>'}
                    </div>
                    <div class="gw-params-actions">
                        <button type="button" class="gw-btn gw-btn-small gw-btn-secondary"
                                onclick="editor.showConfigParamsEditor()">
                            <i class="fas fa-edit"></i> 编辑参数
                        </button>
                        <button type="button" class="gw-btn gw-btn-small gw-btn-primary"
                                onclick="editor.addConfigParam()">
                            <i class="fas fa-plus"></i> 添加参数
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    toggleConfigType(type) {
        document.getElementById('simple-config').style.display = type === 'simple' ? 'block' : 'none';
        document.getElementById('complex-config').style.display = type === 'complex' ? 'block' : 'none';
    }

    // 创建配置参数编辑器
    createConfigParamsEditor(params) {
        let html = '';

        if (params.override_params) {
            html += `
                <div class="gw-config-params-section">
                    <h5 class="gw-config-params-title">
                        <i class="fas fa-cog"></i> 覆盖参数
                    </h5>
                    <div class="gw-config-params-fields">
                        ${Object.entries(params.override_params).map(([key, value]) => `
                            <div class="gw-config-param-field">
                                <span class="gw-config-param-key">${key}:</span>
                                <span class="gw-config-param-value">${JSON.stringify(value)}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        if (params.add_params) {
            html += `
                <div class="gw-config-params-section">
                    <h5 class="gw-config-params-title">
                        <i class="fas fa-plus-circle"></i> 添加参数
                    </h5>
                    <div class="gw-config-params-fields">
                        ${Object.entries(params.add_params).map(([key, value]) => `
                            <div class="gw-config-param-field">
                                <span class="gw-config-param-key">${key}:</span>
                                <span class="gw-config-param-value">${JSON.stringify(value)}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        if (params.remove_params) {
            html += `
                <div class="gw-config-params-section">
                    <h5 class="gw-config-params-title">
                        <i class="fas fa-minus-circle"></i> 移除参数
                    </h5>
                    <div class="gw-config-params-list">
                        ${params.remove_params.map(param => `
                            <span class="gw-config-param-tag">${param}</span>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        return html || '<p class="gw-no-params">暂无附加参数</p>';
    }

    // 显示配置参数编辑器
    showConfigParamsEditor() {
        const currentParams = this.getCurrentConfigParams();
        this.showModal('编辑配置附加参数', this.createAdvancedParamsForm(currentParams), (data) => {
            try {
                const params = JSON.parse(data.params);
                this.updateConfigParamsDisplay(params);
            } catch (error) {
                this.showNotification('JSON格式错误: ' + error.message, 'error');
            }
        });
    }

    // 获取当前配置参数
    getCurrentConfigParams() {
        const paramsEditor = document.querySelector('.gw-config-params-editor');
        if (!paramsEditor) return {};

        // 从当前显示中提取参数（简化实现）
        return this.currentConfigParams || {};
    }

    // 更新配置参数显示
    updateConfigParamsDisplay(params) {
        this.currentConfigParams = params;
        const paramsEditor = document.querySelector('.gw-config-params-editor');
        if (paramsEditor) {
            paramsEditor.innerHTML = this.createConfigParamsEditor(params);
        }
    }

    // 添加配置参数
    addConfigParam() {
        this.showModal('添加配置参数', this.createAddParamForm(), (data) => {
            if (!this.currentConfigParams) {
                this.currentConfigParams = {};
            }

            const section = data.section;
            if (!this.currentConfigParams[section]) {
                if (section === 'remove_params') {
                    this.currentConfigParams[section] = [];
                } else {
                    this.currentConfigParams[section] = {};
                }
            }

            if (section === 'remove_params') {
                this.currentConfigParams[section].push(data.key);
            } else {
                let value = data.value;
                try {
                    // 尝试解析为JSON
                    if (value.startsWith('{') || value.startsWith('[') ||
                        /^\d+$/.test(value) || /^\d*\.\d+$/.test(value) ||
                        value === 'true' || value === 'false') {
                        value = JSON.parse(value);
                    }
                } catch (e) {
                    // 保持字符串格式
                }
                this.currentConfigParams[section][data.key] = value;
            }

            this.updateConfigParamsDisplay(this.currentConfigParams);
        });
    }

    // 提供商管理方法
    addProvider() {
        this.showModal('添加提供商', this.createProviderForm(), (data) => {
            this.config.providers[data.name] = data.config;
            this.renderProviders();
        });
    }

    editProvider(providerName) {
        const provider = this.config.providers[providerName];
        this.showModal('编辑提供商', this.createProviderForm(providerName, provider), (data) => {
            if (data.name !== providerName) {
                delete this.config.providers[providerName];
            }
            this.config.providers[data.name] = data.config;
            this.renderProviders();
        });
    }

    removeProvider(providerName) {
        if (confirm(`确定要删除提供商 "${providerName}" 吗？`)) {
            delete this.config.providers[providerName];
            this.renderProviders();
        }
    }

    createProviderForm(providerName = '', provider = {}) {
        return `
            <div class="gw-form-group">
                <label class="gw-form-label">提供商名称</label>
                <input type="text" class="gw-form-input" id="provider-name" value="${providerName}"
                       placeholder="例如: openai">
            </div>
            <div class="gw-form-group">
                <label class="gw-form-label">提供商类型</label>
                <select class="gw-form-select" id="provider-type">
                    <option value="openai" ${provider.provider === 'openai' ? 'selected' : ''}>OpenAI</option>
                    <option value="anthropic" ${provider.provider === 'anthropic' ? 'selected' : ''}>Anthropic</option>
                    <option value="google" ${provider.provider === 'google' ? 'selected' : ''}>Google</option>
                    <option value="azure-openai" ${provider.provider === 'azure-openai' ? 'selected' : ''}>Azure OpenAI</option>
                    <option value="workers-ai" ${provider.provider === 'workers-ai' ? 'selected' : ''}>Workers AI</option>
                    <option value="other" ${!['openai', 'anthropic', 'google', 'azure-openai', 'workers-ai'].includes(provider.provider) ? 'selected' : ''}>其他</option>
                </select>
            </div>
            <div class="gw-form-group">
                <label class="gw-form-label">API Key</label>
                <input type="text" class="gw-form-input" id="provider-api-key" value="${provider.api_key || ''}"
                       placeholder="sk-...">
            </div>
            <div class="gw-form-group">
                <label class="gw-form-label">自定义主机 (可选)</label>
                <input type="text" class="gw-form-input" id="provider-host" value="${provider.custom_host || ''}"
                       placeholder="https://api.example.com">
            </div>
        `;
    }

    // 模态框管理
    showModal(title, content, onSave) {
        console.log('📋 显示模态框:', title);
        document.getElementById('gw-modal-title').textContent = title;
        document.getElementById('gw-modal-body').innerHTML = content;
        document.getElementById('gw-modal').classList.add('show');

        this.currentModalSave = onSave;

        // 绑定跳转按钮事件
        this.bindModalEvents();
    }

    // 绑定模态框内的事件
    bindModalEvents() {
        console.log('🔗 绑定模态框事件');

        // 绑定跳转到配置按钮
        const jumpBtn = document.getElementById('jump-to-config');
        if (jumpBtn) {
            console.log('✅ 找到跳转按钮，绑定事件');
            jumpBtn.addEventListener('click', () => {
                console.log('🔗 跳转按钮被点击');
                const configSelect = document.getElementById('model-config');
                if (configSelect && configSelect.value) {
                    console.log('🎯 准备跳转到配置:', configSelect.value);
                    this.jumpToConfiguration(configSelect.value);
                } else {
                    console.log('❌ 未选择配置');
                    this.showNotification('请先选择配置', 'warning');
                }
            });
        } else {
            console.log('ℹ️  未找到跳转按钮');
        }

        // 绑定配置选择变化事件
        const configSelect = document.getElementById('model-config');
        if (configSelect) {
            console.log('✅ 找到配置选择器，绑定事件');
            configSelect.addEventListener('change', (e) => {
                console.log('📋 配置选择变化:', e.target.value);
                this.updateConfigPreview(e.target.value);

                // 显示或隐藏跳转按钮
                const jumpBtn = document.getElementById('jump-to-config');
                if (jumpBtn) {
                    jumpBtn.style.display = e.target.value ? 'inline-flex' : 'none';
                    console.log('🔗 跳转按钮显示状态:', e.target.value ? '显示' : '隐藏');
                }
            });
        }
    }

    closeModal() {
        document.getElementById('gw-modal').classList.remove('show');
        this.currentModalSave = null;
    }

    saveModalData() {
        if (!this.currentModalSave) return;

        try {
            let data = {};

            // 根据当前模态框内容收集数据
            const modalBody = document.getElementById('gw-modal-body');

            // 嵌套目标编辑数据
            if (modalBody.querySelector('#has-strategy')) {
                data = this.buildNestedTargetData();
            }
            // 添加目标数据
            else if (modalBody.querySelector('#target-type')) {
                data = this.buildAddTargetData();
            }
            // 高级参数编辑数据
            else if (modalBody.querySelector('#advanced-params')) {
                data = {
                    params: document.getElementById('advanced-params').value
                };
            }
            // 添加参数数据
            else if (modalBody.querySelector('#param-section')) {
                data = this.buildAddParamData();
            }
            // 模型数据
            else if (modalBody.querySelector('#model-name')) {
                data = {
                    name: document.getElementById('model-name').value,
                    configName: document.getElementById('model-config').value,
                    useIn: Array.from(modalBody.querySelectorAll('.gw-tag.selected[data-path]')).map(tag => tag.dataset.path),
                    type: Array.from(modalBody.querySelectorAll('.gw-tag.selected[data-type]')).map(tag => tag.dataset.type)
                };
            }
            // 映射数据
            else if (modalBody.querySelector('#mapping-from')) {
                data = {
                    from: document.getElementById('mapping-from').value,
                    to: document.getElementById('mapping-to').value
                };
            }
            // 配置数据
            else if (modalBody.querySelector('#config-name')) {
                const configType = document.getElementById('config-type').value;
                data = {
                    name: document.getElementById('config-name').value,
                    config: configType === 'simple' ?
                        document.getElementById('provider-ref').value :
                        this.buildComplexConfig()
                };
            }
            // 提供商数据
            else if (modalBody.querySelector('#provider-name')) {
                data = {
                    name: document.getElementById('provider-name').value,
                    config: this.buildProviderConfig()
                };
            }

            this.currentModalSave(data);
            this.closeModal();
        } catch (error) {
            this.showNotification('保存失败: ' + error.message, 'error');
        }
    }

    // 构建嵌套目标数据
    buildNestedTargetData() {
        const data = {};

        // 策略配置
        if (document.getElementById('has-strategy').checked) {
            data.strategy = {
                mode: document.getElementById('target-strategy').value
            };
            // 如果有策略，确保有targets数组
            if (!data.targets) {
                data.targets = [];
            }
        }

        // 基础提供商
        if (document.getElementById('has-base-provider').checked) {
            const provider = document.getElementById('target-base-provider').value;
            if (provider) {
                data.base_provider = provider;
            }
        }

        // 附加参数
        if (document.getElementById('has-params').checked) {
            const paramsStr = document.getElementById('target-params').value;
            if (paramsStr.trim()) {
                data.added_params = JSON.parse(paramsStr);
            }
        }

        return data;
    }

    // 跳转功能
    jumpToConfiguration(configName) {
        console.log('🔗 开始跳转到配置:', configName);

        if (!configName) {
            console.log('❌ 配置名称为空');
            this.showNotification('请先选择配置', 'warning');
            return;
        }

        console.log('📋 当前可用配置:', Object.keys(this.config.configurations || {}));

        // 检查配置是否存在
        if (!this.config.configurations || !this.config.configurations[configName]) {
            console.log('❌ 配置不存在:', configName);
            this.showNotification(`配置 ${configName} 不存在`, 'error');
            return;
        }

        console.log('✅ 配置存在，开始跳转流程');

        // 关闭当前模态框
        console.log('🔒 关闭模态框');
        this.closeModal();

        // 切换到配置策略标签
        console.log('📑 切换到配置策略标签');
        this.switchSection('configurations');

        // 等待渲染完成后滚动到目标配置
        console.log('⏱️  等待渲染完成...');
        setTimeout(() => {
            console.log('🎯 开始滚动到目标配置');
            this.scrollToConfiguration(configName);
        }, 100);
    }

    // 滚动到指定配置
    scrollToConfiguration(configName) {
        console.log('🔍 查找配置元素:', configName);

        // 使用更安全的方法查找元素，避免特殊字符问题
        const configElements = document.querySelectorAll('[data-config]');
        console.log('📋 找到配置元素数量:', configElements.length);

        const availableConfigs = Array.from(configElements).map(el => el.getAttribute('data-config'));
        console.log('📋 可用配置列表:', availableConfigs);

        let configElement = null;

        for (let element of configElements) {
            const elementConfig = element.getAttribute('data-config');
            console.log('🔍 检查元素配置:', elementConfig);
            if (elementConfig === configName) {
                configElement = element;
                console.log('✅ 找到匹配的配置元素');
                break;
            }
        }

        if (configElement) {
            console.log('🎯 开始高亮和滚动');

            // 高亮显示目标配置
            this.highlightElement(configElement);

            // 滚动到目标位置
            configElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            console.log('✅ 跳转完成');
        } else {
            console.log('❌ 未找到配置元素:', configName);
            console.log('📋 可用配置:', availableConfigs);
            this.showNotification(`未找到配置: ${configName}`, 'error');
        }
    }

    // 高亮显示元素
    highlightElement(element) {
        console.log('✨ 开始高亮元素');

        // 移除之前的高亮
        const previousHighlights = document.querySelectorAll('.gw-highlight');
        console.log('🔄 移除之前的高亮数量:', previousHighlights.length);
        previousHighlights.forEach(el => {
            el.classList.remove('gw-highlight');
        });

        // 添加高亮效果
        element.classList.add('gw-highlight');
        console.log('✅ 高亮效果已添加');

        // 3秒后移除高亮
        setTimeout(() => {
            element.classList.remove('gw-highlight');
            console.log('🔄 高亮效果已移除');
        }, 3000);
    }

    // 更新配置预览
    updateConfigPreview(configName) {
        const previewElement = document.getElementById('config-preview');
        const jumpButton = document.getElementById('jump-to-config');

        if (configName && previewElement) {
            previewElement.innerHTML = this.createConfigPreview(configName);
            if (jumpButton) {
                jumpButton.style.display = 'inline-flex';
            }
        } else if (previewElement) {
            previewElement.innerHTML = '';
            if (jumpButton) {
                jumpButton.style.display = 'none';
            }
        }
    }

    // 创建配置预览
    createConfigPreview(configName) {
        const config = this.config.configurations[configName];
        if (!config) return '<p class="gw-preview-empty">配置不存在</p>';

        if (typeof config === 'string') {
            return `
                <div class="gw-config-preview-content">
                    <div class="gw-preview-type">简单引用</div>
                    <div class="gw-preview-value">→ ${config}</div>
                </div>
            `;
        }

        let preview = '<div class="gw-config-preview-content">';

        // 基础提供商
        if (config.base_provider) {
            preview += `<div class="gw-preview-item">
                <span class="gw-preview-label">基础提供商:</span>
                <span class="gw-preview-value">${config.base_provider}</span>
            </div>`;
        }

        // 策略
        if (config.strategy) {
            preview += `<div class="gw-preview-item">
                <span class="gw-preview-label">策略:</span>
                <span class="gw-preview-value">${config.strategy.mode}</span>
            </div>`;
        }

        // 目标数量
        if (config.targets) {
            preview += `<div class="gw-preview-item">
                <span class="gw-preview-label">目标:</span>
                <span class="gw-preview-value">${config.targets.length} 个</span>
            </div>`;
        }

        // 附加参数
        if (config.added_params) {
            const paramTypes = Object.keys(config.added_params);
            preview += `<div class="gw-preview-item">
                <span class="gw-preview-label">参数:</span>
                <span class="gw-preview-value">${paramTypes.join(', ')}</span>
            </div>`;
        }

        preview += '</div>';
        return preview;
    }

    // 跳转到模型
    jumpToModel(modelName) {
        if (!modelName) {
            this.showNotification('请先选择模型', 'warning');
            return;
        }

        // 关闭当前模态框
        this.closeModal();

        // 切换到模型标签
        this.switchSection('models');

        // 等待渲染完成后滚动到目标模型
        setTimeout(() => {
            this.scrollToModel(modelName);
        }, 100);
    }

    // 滚动到指定模型
    scrollToModel(modelName) {
        // 使用更安全的方法查找元素，避免特殊字符问题
        const modelElements = document.querySelectorAll('[data-model]');
        let modelElement = null;

        for (let element of modelElements) {
            if (element.getAttribute('data-model') === modelName) {
                modelElement = element;
                break;
            }
        }

        if (modelElement) {
            // 高亮显示目标模型
            this.highlightElement(modelElement);

            // 滚动到目标位置
            modelElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        } else {
            this.showNotification(`未找到模型: ${modelName}`, 'error');
            console.log('Available models:', Array.from(modelElements).map(el => el.getAttribute('data-model')));
        }
    }



    // 构建添加目标数据
    buildAddTargetData() {
        const targetType = document.getElementById('target-type').value;

        if (targetType === 'simple') {
            return {
                type: 'simple',
                value: document.getElementById('simple-target-value').value
            };
        } else {
            const data = { type: 'complex' };

            // 策略配置
            if (document.getElementById('add-strategy').checked) {
                data.strategy = document.getElementById('add-target-strategy').value;
            }

            // 基础提供商
            if (document.getElementById('add-base-provider').checked) {
                const provider = document.getElementById('add-target-base-provider').value;
                if (provider) {
                    data.baseProvider = provider;
                }
            }

            // 附加参数
            if (document.getElementById('add-params').checked) {
                const paramsStr = document.getElementById('add-target-params').value;
                if (paramsStr.trim()) {
                    data.params = paramsStr;
                }
            }

            return data;
        }
    }

    // 构建添加参数数据
    buildAddParamData() {
        const section = document.getElementById('param-section').value;

        if (section === 'remove_params') {
            return {
                section: section,
                key: document.getElementById('remove-param-key').value
            };
        } else {
            return {
                section: section,
                key: document.getElementById('param-key').value,
                value: document.getElementById('param-value').value
            };
        }
    }

    buildComplexConfig() {
        const config = {};

        // 基础提供商
        const baseProvider = document.getElementById('base-provider').value;
        if (baseProvider) {
            config.base_provider = baseProvider;
        }

        // 策略模式
        const strategyMode = document.getElementById('strategy-mode').value;
        if (strategyMode) {
            config.strategy = { mode: strategyMode };
        }

        // 重试次数
        const retryAttempts = document.getElementById('retry-attempts').value;
        if (retryAttempts) {
            config.retry = { attempts: parseInt(retryAttempts) };
        }

        // 目标列表
        const targetsList = document.getElementById('targets-list').value;
        if (targetsList.trim()) {
            config.targets = targetsList.split('\n').map(t => t.trim()).filter(t => t);
        }

        // 附加参数
        if (this.currentConfigParams && Object.keys(this.currentConfigParams).length > 0) {
            config.added_params = this.currentConfigParams;
        }

        return config;
    }

    buildProviderConfig() {
        const config = {};

        config.provider = document.getElementById('provider-type').value;
        config.api_key = document.getElementById('provider-api-key').value;

        const customHost = document.getElementById('provider-host').value;
        if (customHost) {
            config.custom_host = customHost;
        }

        return config;
    }

    // 搜索和过滤
    filterModels(searchTerm) {
        const items = document.querySelectorAll('.gw-model-item');
        items.forEach(item => {
            const modelName = item.dataset.model.toLowerCase();
            const visible = modelName.includes(searchTerm.toLowerCase());
            item.style.display = visible ? 'block' : 'none';
        });
    }

    filterProviders(searchTerm) {
        const items = document.querySelectorAll('.gw-provider-item');
        items.forEach(item => {
            const providerName = item.dataset.provider.toLowerCase();
            const visible = providerName.includes(searchTerm.toLowerCase());
            item.style.display = visible ? 'block' : 'none';
        });
    }

    // 通知系统
    showNotification(message, type = 'success') {
        const notification = document.getElementById('gw-notification');
        const textElement = document.getElementById('gw-notification-text');

        textElement.textContent = message;
        notification.className = `gw-notification ${type}`;
        notification.classList.add('show');

        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }

    // 加载指示器
    showLoading(show) {
        const loading = document.getElementById('gw-loading');
        if (show) {
            loading.classList.add('show');
        } else {
            loading.classList.remove('show');
        }
    }

    // 拖拽排序功能
    initSortable(container, onSort) {
        let draggedElement = null;
        let draggedIndex = -1;

        container.addEventListener('dragstart', (e) => {
            if (e.target.classList.contains('gw-sortable-item')) {
                draggedElement = e.target;
                draggedIndex = Array.from(container.children).indexOf(draggedElement);
                e.target.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
            }
        });

        container.addEventListener('dragend', (e) => {
            if (e.target.classList.contains('gw-sortable-item')) {
                e.target.classList.remove('dragging');
                draggedElement = null;
                draggedIndex = -1;
            }
        });

        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            const afterElement = this.getDragAfterElement(container, e.clientY);
            if (afterElement == null) {
                container.appendChild(draggedElement);
            } else {
                container.insertBefore(draggedElement, afterElement);
            }
        });

        container.addEventListener('drop', (e) => {
            e.preventDefault();
            if (draggedElement) {
                const newIndex = Array.from(container.children).indexOf(draggedElement);
                if (newIndex !== draggedIndex && onSort) {
                    onSort(draggedIndex, newIndex);
                }
            }
        });
    }

    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.gw-sortable-item:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }



    // 初始化所有可排序容器
    initAllSortables() {
        // 为每个嵌套目标容器初始化拖拽排序
        document.querySelectorAll('.gw-nested-targets').forEach(container => {
            const level = parseInt(container.dataset.level) || 0;
            const configName = this.findConfigName(container);

            if (configName) {
                this.initNestedSortable(container, configName, level);
            }
        });
    }

    // 查找配置名称
    findConfigName(element) {
        let current = element;
        while (current && current !== document.body) {
            if (current.classList.contains('gw-config-body')) {
                const configItem = current.closest('.gw-config-item');
                if (configItem) {
                    const titleElement = configItem.querySelector('.gw-config-title');
                    if (titleElement) {
                        return titleElement.textContent.trim();
                    }
                }
            }
            current = current.parentElement;
        }
        return null;
    }

    // 初始化嵌套拖拽排序
    initNestedSortable(container, configName, level) {
        let draggedElement = null;
        let draggedIndex = -1;

        container.addEventListener('dragstart', (e) => {
            if (e.target.classList.contains('gw-target-item') && e.target.draggable) {
                draggedElement = e.target;
                draggedIndex = Array.from(container.children)
                    .filter(child => child.classList.contains('gw-target-item'))
                    .indexOf(draggedElement);
                e.target.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
            }
        });

        container.addEventListener('dragend', (e) => {
            if (e.target.classList.contains('gw-target-item')) {
                e.target.classList.remove('dragging');
                draggedElement = null;
                draggedIndex = -1;
            }
        });

        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            if (draggedElement) {
                const afterElement = this.getDragAfterElement(container, e.clientY);
                if (afterElement == null) {
                    container.appendChild(draggedElement);
                } else {
                    container.insertBefore(draggedElement, afterElement);
                }
            }
        });

        container.addEventListener('drop', (e) => {
            e.preventDefault();
            if (draggedElement) {
                const targetItems = Array.from(container.children)
                    .filter(child => child.classList.contains('gw-target-item'));
                const newIndex = targetItems.indexOf(draggedElement);

                if (newIndex !== draggedIndex && newIndex >= 0) {
                    this.reorderNestedTargets(configName, container, draggedIndex, newIndex);
                }
            }
        });
    }

    // 重新排序嵌套目标
    reorderNestedTargets(configName, container, oldIndex, newIndex) {
        // 找到对应的配置路径
        const path = this.getContainerPath(container);
        const config = this.config.configurations[configName];
        let targets = config.targets;

        // 导航到正确的targets数组
        for (let i = 0; i < path.length; i++) {
            const segment = path[i];
            if (segment === 'targets') {
                continue;
            }
            targets = targets[segment];
            if (targets.targets) {
                targets = targets.targets;
            }
        }

        // 重新排序
        const movedItem = targets.splice(oldIndex, 1)[0];
        targets.splice(newIndex, 0, movedItem);

        console.log(`Reordered nested targets for ${configName}:`, targets);
    }

    // 获取容器路径
    getContainerPath(container) {
        const path = [];
        let current = container;

        while (current && !current.classList.contains('gw-config-body')) {
            if (current.classList.contains('gw-nested-targets')) {
                const level = parseInt(current.dataset.level) || 0;
                // 根据层级推断路径
                for (let i = 0; i < level; i++) {
                    path.unshift('targets');
                }
                break;
            }
            current = current.parentElement;
        }

        return path;
    }
}

// 全局实例
let editor;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    editor = new GatewayConfigEditor();
});
