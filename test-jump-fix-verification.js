// 验证跳转功能修复
const fs = require('fs');

console.log('🔧 验证跳转功能修复');
console.log('===================');

// 检查代码修复
function checkCodeFixes() {
    console.log('\n1. 检查代码修复...');
    
    const jsContent = fs.readFileSync('./config-editor.js', 'utf8');
    
    const fixes = {
        switchSectionCall: {
            description: '跳转功能调用正确的方法',
            pattern: /this\.switchSection\('configurations'\)/,
            expected: true
        },
        noSwitchTabCall: {
            description: '不再调用错误的switchTab方法',
            pattern: /this\.switchTab\('configurations'\)/,
            expected: false
        },
        dataConfigAttribute: {
            description: '配置项包含data-config属性',
            pattern: /data-config="\$\{key\}"/,
            expected: true
        },
        safeElementSearch: {
            description: '使用安全的元素查找方法',
            pattern: /document\.querySelectorAll\('\[data-config\]'\)/,
            expected: true
        },
        highlightMethod: {
            description: '包含高亮显示方法',
            pattern: /highlightElement\(element\)/,
            expected: true
        },
        configLinksBinding: {
            description: '包含配置链接绑定',
            pattern: /bindConfigLinks\(\)/,
            expected: true
        }
    };
    
    let passedFixes = 0;
    let totalFixes = Object.keys(fixes).length;
    
    Object.entries(fixes).forEach(([key, fix]) => {
        const found = fix.pattern.test(jsContent);
        const passed = found === fix.expected;
        
        console.log(`   ${passed ? '✅' : '❌'} ${fix.description}: ${found ? '找到' : '未找到'}`);
        
        if (passed) passedFixes++;
    });
    
    console.log(`\n📊 代码修复检查: ${passedFixes}/${totalFixes} 通过`);
    return passedFixes === totalFixes;
}

// 检查CSS样式
function checkCSSStyles() {
    console.log('\n2. 检查CSS样式...');
    
    const cssContent = fs.readFileSync('./config-editor.css', 'utf8');
    
    const styles = {
        highlightClass: {
            description: '高亮样式类',
            pattern: /\.gw-highlight/,
            expected: true
        },
        highlightAnimation: {
            description: '高亮动画',
            pattern: /@keyframes highlight/,
            expected: true
        },
        configLink: {
            description: '配置链接样式',
            pattern: /\.gw-config-link/,
            expected: true
        }
    };
    
    let passedStyles = 0;
    let totalStyles = Object.keys(styles).length;
    
    Object.entries(styles).forEach(([key, style]) => {
        const found = style.pattern.test(cssContent);
        const passed = found === style.expected;
        
        console.log(`   ${passed ? '✅' : '❌'} ${style.description}: ${found ? '找到' : '未找到'}`);
        
        if (passed) passedStyles++;
    });
    
    console.log(`\n📊 CSS样式检查: ${passedStyles}/${totalStyles} 通过`);
    return passedStyles === totalStyles;
}

// 检查HTML结构
function checkHTMLStructure() {
    console.log('\n3. 检查HTML结构...');
    
    const htmlContent = fs.readFileSync('./config-editor.html', 'utf8');
    
    const structures = {
        navigationItems: {
            description: '导航项使用data-section属性',
            pattern: /data-section="configurations"/,
            expected: true
        },
        modalStructure: {
            description: '模态框结构完整',
            pattern: /id="gw-modal"/,
            expected: true
        },
        sectionsExist: {
            description: '所有页面section存在',
            pattern: /id="configurations"/,
            expected: true
        }
    };
    
    let passedStructures = 0;
    let totalStructures = Object.keys(structures).length;
    
    Object.entries(structures).forEach(([key, structure]) => {
        const found = structure.pattern.test(htmlContent);
        const passed = found === structure.expected;
        
        console.log(`   ${passed ? '✅' : '❌'} ${structure.description}: ${found ? '找到' : '未找到'}`);
        
        if (passed) passedStructures++;
    });
    
    console.log(`\n📊 HTML结构检查: ${passedStructures}/${totalStructures} 通过`);
    return passedStructures === totalStructures;
}

// 生成测试报告
function generateTestReport(codePass, cssPass, htmlPass) {
    console.log('\n📋 修复验证报告');
    console.log('================');
    
    const overallPass = codePass && cssPass && htmlPass;
    
    console.log(`\n🔧 代码修复: ${codePass ? '✅ 通过' : '❌ 失败'}`);
    console.log(`🎨 CSS样式: ${cssPass ? '✅ 通过' : '❌ 失败'}`);
    console.log(`📄 HTML结构: ${htmlPass ? '✅ 通过' : '❌ 失败'}`);
    
    console.log(`\n🎯 总体状态: ${overallPass ? '✅ 修复成功' : '❌ 需要进一步修复'}`);
    
    if (overallPass) {
        console.log('\n🎉 跳转功能修复验证通过！');
        console.log('\n✨ 修复内容总结:');
        console.log('1. ✅ 修正了switchTab调用为switchSection');
        console.log('2. ✅ 确保配置项包含data-config属性');
        console.log('3. ✅ 使用安全的元素查找方法');
        console.log('4. ✅ 包含完整的高亮显示功能');
        console.log('5. ✅ 配置链接事件绑定正常');
        console.log('6. ✅ CSS高亮样式完整');
        
        console.log('\n🚀 使用说明:');
        console.log('1. 确保服务器正在运行: npm start');
        console.log('2. 访问编辑器: http://localhost:3474/config-editor.html');
        console.log('3. 测试模型编辑中的配置跳转');
        console.log('4. 测试模型列表中的配置链接');
        console.log('5. 验证跳转后的高亮效果');
        
        console.log('\n💡 预期行为:');
        console.log('- 点击跳转按钮应该切换到配置页面');
        console.log('- 目标配置应该被高亮显示');
        console.log('- 页面应该自动滚动到目标位置');
        console.log('- 浏览器控制台应该显示详细日志');
        
    } else {
        console.log('\n❌ 发现问题，需要进一步修复');
        console.log('\n🔧 建议检查:');
        if (!codePass) console.log('- 检查JavaScript代码中的方法调用');
        if (!cssPass) console.log('- 检查CSS样式文件');
        if (!htmlPass) console.log('- 检查HTML结构');
    }
    
    return overallPass;
}

// 主验证函数
async function runVerification() {
    try {
        console.log('开始验证跳转功能修复...\n');
        
        const codePass = checkCodeFixes();
        const cssPass = checkCSSStyles();
        const htmlPass = checkHTMLStructure();
        
        const success = generateTestReport(codePass, cssPass, htmlPass);
        
        if (success) {
            console.log('\n🎊 恭喜！跳转功能修复验证完成！');
            console.log('现在可以测试实际的跳转功能了。');
        } else {
            console.log('\n⚠️  验证未完全通过，请检查上述问题。');
        }
        
    } catch (error) {
        console.log('❌ 验证过程中出现错误:', error.message);
        console.log('\n🔧 建议:');
        console.log('1. 确保所有文件都存在');
        console.log('2. 检查文件权限');
        console.log('3. 确保文件格式正确');
    }
}

// 运行验证
runVerification();
