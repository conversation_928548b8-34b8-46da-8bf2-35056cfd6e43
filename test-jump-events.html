<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳转事件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            display: block;
            width: 100%;
        }
        .test-button:hover {
            background: #005a87;
        }
        .test-log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 跳转事件测试</h1>
        
        <div class="status info">
            <strong>测试说明：</strong><br>
            这个页面用于测试跳转功能的事件绑定是否正常工作。<br>
            请先确保主编辑器已经加载，然后点击下面的测试按钮。
        </div>
        
        <button class="test-button" onclick="testEditorExists()">
            1. 测试编辑器对象是否存在
        </button>
        
        <button class="test-button" onclick="testJumpFunction()">
            2. 测试跳转函数是否可调用
        </button>
        
        <button class="test-button" onclick="testModalEvents()">
            3. 测试模态框事件绑定
        </button>
        
        <button class="test-button" onclick="testConfigLinks()">
            4. 测试配置链接事件
        </button>
        
        <button class="test-button" onclick="simulateJump()">
            5. 模拟完整跳转流程
        </button>
        
        <button class="test-button" onclick="clearLog()">
            清空日志
        </button>
        
        <div id="test-log" class="test-log">等待测试...</div>
    </div>

    <script>
        let testLog = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            testLog.push(logMessage);
            document.getElementById('test-log').textContent = testLog.join('\n');
            console.log(logMessage);
        }
        
        function clearLog() {
            testLog = [];
            document.getElementById('test-log').textContent = '日志已清空';
        }
        
        function testEditorExists() {
            log('🔍 测试编辑器对象是否存在...');
            
            if (typeof window.parent !== 'undefined' && window.parent !== window) {
                // 在iframe中，尝试访问父窗口的editor
                try {
                    const parentEditor = window.parent.editor;
                    if (parentEditor) {
                        log('✅ 在父窗口中找到编辑器对象');
                        window.testEditor = parentEditor;
                        return true;
                    }
                } catch (e) {
                    log('❌ 无法访问父窗口的编辑器对象: ' + e.message);
                }
            }
            
            // 尝试直接访问
            if (typeof editor !== 'undefined') {
                log('✅ 找到编辑器对象');
                window.testEditor = editor;
                return true;
            }
            
            // 尝试从window对象访问
            if (window.editor) {
                log('✅ 在window对象中找到编辑器');
                window.testEditor = window.editor;
                return true;
            }
            
            log('❌ 未找到编辑器对象');
            log('💡 请确保主编辑器页面已经加载');
            return false;
        }
        
        function testJumpFunction() {
            log('🔍 测试跳转函数是否可调用...');
            
            if (!window.testEditor) {
                if (!testEditorExists()) {
                    return;
                }
            }
            
            if (typeof window.testEditor.jumpToConfiguration === 'function') {
                log('✅ jumpToConfiguration 函数存在');
                
                // 测试函数调用（不传参数）
                try {
                    window.testEditor.jumpToConfiguration('');
                    log('✅ 函数可以调用（空参数测试）');
                } catch (e) {
                    log('❌ 函数调用失败: ' + e.message);
                }
            } else {
                log('❌ jumpToConfiguration 函数不存在');
            }
        }
        
        function testModalEvents() {
            log('🔍 测试模态框事件绑定...');
            
            if (!window.testEditor) {
                if (!testEditorExists()) {
                    return;
                }
            }
            
            if (typeof window.testEditor.bindModalEvents === 'function') {
                log('✅ bindModalEvents 函数存在');
                
                try {
                    window.testEditor.bindModalEvents();
                    log('✅ bindModalEvents 函数可以调用');
                } catch (e) {
                    log('❌ bindModalEvents 调用失败: ' + e.message);
                }
            } else {
                log('❌ bindModalEvents 函数不存在');
            }
        }
        
        function testConfigLinks() {
            log('🔍 测试配置链接事件...');
            
            if (!window.testEditor) {
                if (!testEditorExists()) {
                    return;
                }
            }
            
            if (typeof window.testEditor.bindConfigLinks === 'function') {
                log('✅ bindConfigLinks 函数存在');
                
                try {
                    window.testEditor.bindConfigLinks();
                    log('✅ bindConfigLinks 函数可以调用');
                } catch (e) {
                    log('❌ bindConfigLinks 调用失败: ' + e.message);
                }
            } else {
                log('❌ bindConfigLinks 函数不存在');
            }
        }
        
        function simulateJump() {
            log('🔍 模拟完整跳转流程...');
            
            if (!window.testEditor) {
                if (!testEditorExists()) {
                    return;
                }
            }
            
            // 检查配置数据
            if (window.testEditor.config && window.testEditor.config.configurations) {
                const configs = Object.keys(window.testEditor.config.configurations);
                log('📋 可用配置: ' + configs.join(', '));
                
                if (configs.length > 0) {
                    const testConfig = configs[0];
                    log('🎯 使用测试配置: ' + testConfig);
                    
                    try {
                        window.testEditor.jumpToConfiguration(testConfig);
                        log('✅ 跳转函数调用成功');
                    } catch (e) {
                        log('❌ 跳转失败: ' + e.message);
                    }
                } else {
                    log('❌ 没有可用的配置进行测试');
                }
            } else {
                log('❌ 配置数据不可用');
            }
        }
        
        // 页面加载时自动运行基础测试
        window.addEventListener('load', () => {
            log('🚀 页面加载完成，开始自动测试...');
            setTimeout(() => {
                testEditorExists();
            }, 1000);
        });
        
        // 监听来自父窗口的消息
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'editor-ready') {
                log('📨 收到编辑器就绪消息');
                testEditorExists();
            }
        });
    </script>
</body>
</html>
