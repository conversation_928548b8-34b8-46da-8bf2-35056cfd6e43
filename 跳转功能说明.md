# 跳转功能说明

## 🎯 功能概述

配置编辑器现在提供了智能跳转功能，可以在不同的配置项、模型和映射之间快速导航，大大提升了用户体验和操作效率。

## 🔗 支持的跳转功能

### 1. 模型编辑中的配置跳转
在编辑模型时，可以直接跳转到关联的配置：
- **位置**: 模型编辑对话框 → 配置名称选择器
- **功能**: 选择配置后显示"跳转"按钮
- **效果**: 点击跳转到对应的配置项并高亮显示

### 2. 模型列表中的配置链接
在模型列表中，配置名称变成可点击的链接：
- **位置**: 模型管理 → 模型列表 → 配置名称
- **功能**: 配置名称显示为蓝色链接，带有跳转图标
- **效果**: 点击直接跳转到对应配置

### 3. 模型映射中的模型跳转
在模型映射中，可以跳转到关联的模型：
- **位置**: 模型映射 → 映射列表 → 模型名称
- **功能**: 如果模型存在，显示跳转按钮
- **效果**: 点击跳转到对应的模型并高亮显示

### 4. 配置预览功能
在模型编辑时，实时预览选中的配置信息：
- **位置**: 模型编辑对话框 → 配置预览区域
- **功能**: 显示配置的基本信息（类型、策略、目标数量等）
- **效果**: 帮助用户快速了解配置内容

## 🎨 界面元素

### 跳转按钮
- **图标**: 🔗 外部链接图标
- **颜色**: 蓝色主题
- **动画**: 悬停时有光泽扫过效果
- **位置**: 选择器旁边或链接内部

### 配置链接
- **样式**: 蓝色文字，下划线
- **图标**: 小型外部链接图标
- **交互**: 悬停时颜色加深

### 高亮效果
- **动画**: 3秒渐变高亮动画
- **颜色**: 蓝色边框和背景
- **效果**: 帮助用户快速定位目标项

## 📋 使用方法

### 从模型跳转到配置
1. 进入"模型管理"部分
2. 点击模型的"编辑"按钮
3. 在配置名称选择器中选择配置
4. 点击"跳转"按钮
5. 自动切换到"配置策略"标签并定位到目标配置

### 从模型列表跳转到配置
1. 进入"模型管理"部分
2. 在模型列表中找到目标模型
3. 点击配置名称链接（蓝色文字）
4. 自动跳转到对应的配置项

### 从模型映射跳转到模型
1. 进入"模型映射"部分
2. 在映射列表中找到目标映射
3. 点击模型名称旁的跳转按钮
4. 自动切换到"模型管理"标签并定位到目标模型

### 查看配置预览
1. 在模型编辑对话框中
2. 选择不同的配置名称
3. 在下方的预览区域查看配置信息
4. 包括：基础提供商、策略、目标数量、附加参数等

## 🔧 技术实现

### 跳转逻辑
```javascript
// 跳转到配置
jumpToConfiguration(configName) {
    this.closeModal();           // 关闭当前对话框
    this.switchTab('configurations'); // 切换到配置标签
    this.scrollToConfiguration(configName); // 滚动并高亮
}

// 跳转到模型
jumpToModel(modelName) {
    this.closeModal();
    this.switchTab('models');
    this.scrollToModel(modelName);
}
```

### 高亮效果
```css
.gw-highlight {
    animation: highlight 3s ease-in-out;
    border: 2px solid #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}
```

### 配置预览
```javascript
createConfigPreview(configName) {
    // 显示配置的基本信息
    // 包括类型、策略、目标、参数等
}
```

## 💡 使用场景

### 配置管理场景
1. **查看模型配置**: 在模型列表中快速跳转到配置详情
2. **配置验证**: 编辑模型时预览配置信息确保正确性
3. **配置调试**: 快速在模型和配置之间切换进行调试

### 映射管理场景
1. **映射验证**: 检查映射的源模型和目标模型是否存在
2. **模型查看**: 从映射快速跳转到模型详情
3. **关系梳理**: 理清模型之间的映射关系

### 导航场景
1. **快速定位**: 在复杂配置中快速找到目标项
2. **关联查看**: 查看相关联的配置和模型
3. **工作流优化**: 减少手动查找的时间

## ⚠️ 注意事项

### 跳转条件
- **配置跳转**: 配置必须存在于配置列表中
- **模型跳转**: 模型必须存在于模型列表中
- **按钮显示**: 只有存在目标时才显示跳转按钮

### 界面状态
- **标签切换**: 跳转会自动切换到对应的标签页
- **滚动定位**: 自动滚动到目标项的可视区域
- **高亮时间**: 高亮效果持续3秒后自动消失

### 性能考虑
- **延迟执行**: 跳转操作有100ms延迟确保DOM渲染完成
- **动画优化**: 使用CSS动画而非JavaScript动画
- **内存管理**: 及时清理高亮效果避免内存泄漏

## 🚀 未来扩展

### 可能的增强功能
1. **面包屑导航**: 显示当前位置和导航路径
2. **历史记录**: 记录跳转历史，支持前进后退
3. **批量跳转**: 支持批量选择和跳转
4. **搜索跳转**: 结合搜索功能的智能跳转
5. **快捷键**: 支持键盘快捷键进行跳转

### 扩展场景
1. **提供商跳转**: 从配置跳转到提供商详情
2. **路径跳转**: 从模型跳转到使用路径
3. **依赖关系**: 显示和跳转到依赖的其他配置

## 🎉 功能优势

1. **提升效率**: 减少手动查找时间，提高操作效率
2. **改善体验**: 直观的跳转操作，降低使用门槛
3. **减少错误**: 通过预览和跳转减少配置错误
4. **增强理解**: 帮助用户理解配置之间的关联关系
5. **简化操作**: 复杂配置的管理变得更加简单

现在您可以在配置编辑器中享受流畅的跳转体验，快速在相关配置之间导航！
