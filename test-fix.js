// 测试修复后的服务器
const http = require('http');

console.log('🧪 测试服务器修复...');

// 测试基本连接
function testConnection() {
    return new Promise((resolve, reject) => {
        const req = http.get('http://localhost:3474/', (res) => {
            console.log('✅ 服务器响应正常，状态码:', res.statusCode);
            resolve(res.statusCode);
        });
        
        req.on('error', (err) => {
            console.log('❌ 连接失败:', err.message);
            reject(err);
        });
        
        req.setTimeout(5000, () => {
            console.log('❌ 连接超时');
            req.destroy();
            reject(new Error('Timeout'));
        });
    });
}

// 测试静态文件
function testStaticFile() {
    return new Promise((resolve, reject) => {
        const req = http.get('http://localhost:3474/login.html', (res) => {
            console.log('✅ 静态文件服务正常，状态码:', res.statusCode);
            resolve(res.statusCode);
        });
        
        req.on('error', (err) => {
            console.log('❌ 静态文件访问失败:', err.message);
            reject(err);
        });
    });
}

// 测试API端点
function testAPI() {
    return new Promise((resolve, reject) => {
        const req = http.get('http://localhost:3474/api/config', (res) => {
            console.log('✅ API端点响应正常，状态码:', res.statusCode);
            if (res.statusCode === 401) {
                console.log('   (401是预期的，因为没有认证)');
            }
            resolve(res.statusCode);
        });
        
        req.on('error', (err) => {
            console.log('❌ API访问失败:', err.message);
            reject(err);
        });
    });
}

async function runTests() {
    try {
        console.log('\n1. 测试基本连接...');
        await testConnection();
        
        console.log('\n2. 测试静态文件服务...');
        await testStaticFile();
        
        console.log('\n3. 测试API端点...');
        await testAPI();
        
        console.log('\n🎉 所有测试通过！服务器运行正常。');
        console.log('\n📝 建议：');
        console.log('1. 访问 http://localhost:3474/login.html 进行登录');
        console.log('2. 使用默认密码 admin123 或 .env 中配置的密码');
        console.log('3. 登录后即可正常使用配置编辑器');
        
    } catch (error) {
        console.log('\n❌ 测试失败:', error.message);
        console.log('\n🔧 建议：');
        console.log('1. 确保服务器正在运行 (npm start)');
        console.log('2. 检查端口 3474 是否被占用');
        console.log('3. 查看服务器日志是否有错误信息');
    }
}

runTests();
