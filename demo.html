<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gateway 配置编辑器 - 演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .demo-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        .demo-header p {
            margin: 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .demo-content {
            padding: 40px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #667eea;
        }
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .feature-desc {
            color: #666;
            line-height: 1.6;
        }
        .demo-buttons {
            text-align: center;
            margin: 40px 0;
        }
        .demo-btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 0 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        .demo-btn:hover {
            background: #5a6fd8;
        }
        .demo-btn.secondary {
            background: #6c757d;
        }
        .demo-btn.secondary:hover {
            background: #5a6268;
        }
        .screenshot {
            margin: 40px 0;
            text-align: center;
        }
        .screenshot img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .tech-specs {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            margin: 40px 0;
        }
        .tech-specs h3 {
            margin-top: 0;
            color: #333;
        }
        .tech-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .tech-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .tech-item i {
            color: #28a745;
        }
        @media (max-width: 768px) {
            .demo-header {
                padding: 30px 20px;
            }
            .demo-header h1 {
                font-size: 2rem;
            }
            .demo-content {
                padding: 30px 20px;
            }
            .feature-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .demo-btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-cogs"></i> Gateway 配置编辑器</h1>
            <p>现代化的可视化网关配置管理工具</p>
        </div>
        
        <div class="demo-content">
            <div class="demo-buttons">
                <a href="config-editor.html" class="demo-btn">
                    <i class="fas fa-rocket"></i> 启动编辑器
                </a>
                <a href="README_配置编辑器使用说明.md" class="demo-btn secondary">
                    <i class="fas fa-book"></i> 查看文档
                </a>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="feature-title">可视化编辑</div>
                    <div class="feature-desc">
                        直观的界面设计，让复杂的JSON配置变得简单易懂。支持实时预览和验证。
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-title">响应式设计</div>
                    <div class="feature-desc">
                        完美适配PC和移动设备，随时随地管理配置。现代化的UI设计语言。
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-arrows-alt"></i>
                    </div>
                    <div class="feature-title">拖拽排序</div>
                    <div class="feature-desc">
                        fallback模式下支持拖拽排序，直观调整故障转移顺序。操作简单高效。
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="feature-title">智能标签</div>
                    <div class="feature-desc">
                        模型使用范围的点选式编辑，自动从网关路径获取候选项。操作便捷。
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="feature-title">实时搜索</div>
                    <div class="feature-desc">
                        快速搜索和过滤配置项，支持模糊匹配。大量配置也能轻松管理。
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="feature-title">安全可靠</div>
                    <div class="feature-desc">
                        自动备份、配置验证、错误提示。确保配置文件的完整性和正确性。
                    </div>
                </div>
            </div>
            
            <div class="tech-specs">
                <h3><i class="fas fa-code"></i> 技术特性</h3>
                <div class="tech-list">
                    <div class="tech-item">
                        <i class="fas fa-check"></i>
                        <span>原生JavaScript，无框架依赖</span>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-check"></i>
                        <span>CSS前缀隔离，安全集成</span>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-check"></i>
                        <span>Express.js后端API</span>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-check"></i>
                        <span>自动配置文件检测</span>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-check"></i>
                        <span>实时配置验证</span>
                    </div>
                    <div class="tech-item">
                        <i class="fas fa-check"></i>
                        <span>自动备份机制</span>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 40px 0; color: #666;">
                <p><i class="fas fa-info-circle"></i>
                   支持的配置文件路径：当前目录的 conf.json 或 /root/test/gateway/conf.json</p>
                <p><i class="fas fa-server"></i>
                   服务端口：3474 | 访问地址：http://localhost:3474/config-editor.html</p>
                <p><i class="fas fa-shield-alt"></i>
                   默认登录密码：admin123 (可在 .env 文件中修改)</p>
            </div>
        </div>
    </div>
    
    <script>
        // 检查服务器状态
        fetch('/api/config')
            .then(response => {
                if (response.ok) {
                    console.log('✅ 配置编辑器服务正常运行');
                } else {
                    console.warn('⚠️ 配置编辑器服务可能未启动');
                }
            })
            .catch(error => {
                console.warn('⚠️ 无法连接到配置编辑器服务，请确保服务已启动');
            });
    </script>
</body>
</html>
