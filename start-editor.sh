#!/bin/bash

# Gateway Config Editor 启动脚本

echo "==================================="
echo "  Gateway Config Editor"
echo "==================================="
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

echo "✅ npm 版本: $(npm --version)"

# 检查配置文件是否存在
CONFIG_FILE="/root/test/gateway/conf.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "⚠️  配置文件不存在: $CONFIG_FILE"
    echo "   编辑器将使用示例配置运行"
else
    echo "✅ 配置文件存在: $CONFIG_FILE"
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo ""
    echo "📦 安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
fi

# 检查端口是否被占用
PORT=3474
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口 $PORT 已被占用，尝试终止占用进程..."
    lsof -ti:$PORT | xargs kill -9 2>/dev/null
    sleep 2
fi

echo ""
echo "🚀 启动配置编辑器..."
echo "   端口: $PORT"
echo "   登录页面: http://localhost:$PORT/login.html"
echo "   编辑器页面: http://localhost:$PORT/config-editor.html"
echo "   演示页面: http://localhost:$PORT/demo.html"
echo "   默认密码: admin123 (可在 .env 文件中修改)"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 启动服务器
node config-api.js
