<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳转功能调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .debug-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-button:hover {
            background: #005a87;
        }
        .debug-log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .debug-input {
            width: 200px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 跳转功能调试工具</h1>
        
        <div class="debug-section">
            <div class="debug-title">📋 系统状态检查</div>
            <button class="debug-button" onclick="checkSystemStatus()">检查系统状态</button>
            <button class="debug-button" onclick="checkDOMElements()">检查DOM元素</button>
            <button class="debug-button" onclick="checkConfigData()">检查配置数据</button>
            <div id="system-status" class="debug-log"></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">🔗 跳转功能测试</div>
            <input type="text" id="config-name" class="debug-input" placeholder="输入配置名称" value="common-config-28">
            <button class="debug-button" onclick="testJumpToConfig()">测试跳转到配置</button>
            <button class="debug-button" onclick="testSwitchTab()">测试切换标签</button>
            <button class="debug-button" onclick="testScrollToElement()">测试滚动定位</button>
            <div id="jump-status" class="debug-log"></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">📊 实时日志</div>
            <button class="debug-button" onclick="clearLogs()">清空日志</button>
            <button class="debug-button" onclick="exportLogs()">导出日志</button>
            <div id="real-time-logs" class="debug-log"></div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">🎯 快速操作</div>
            <button class="debug-button" onclick="openMainEditor()">打开主编辑器</button>
            <button class="debug-button" onclick="simulateModelEdit()">模拟模型编辑</button>
            <button class="debug-button" onclick="simulateConfigClick()">模拟配置点击</button>
        </div>
    </div>

    <script>
        let logs = [];
        
        // 拦截console.log并显示在页面上
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            logs.push({
                timestamp: new Date().toLocaleTimeString(),
                message: message
            });
            
            updateRealTimeLogs();
        };
        
        function updateRealTimeLogs() {
            const logContainer = document.getElementById('real-time-logs');
            logContainer.textContent = logs.slice(-20).map(log => 
                `[${log.timestamp}] ${log.message}`
            ).join('\n');
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function checkSystemStatus() {
            const statusContainer = document.getElementById('system-status');
            let status = '';
            
            // 检查编辑器对象
            if (typeof editor !== 'undefined') {
                status += '✅ 编辑器对象存在\n';
                status += `📊 配置数量: ${Object.keys(editor.config?.configurations || {}).length}\n`;
                status += `📊 模型数量: ${Object.keys(editor.config?.models || {}).length}\n`;
            } else {
                status += '❌ 编辑器对象不存在\n';
            }
            
            // 检查标签页
            const tabs = document.querySelectorAll('.gw-tab');
            const contents = document.querySelectorAll('.gw-tab-content');
            status += `📑 标签页数量: ${tabs.length}\n`;
            status += `📄 内容区数量: ${contents.length}\n`;
            
            // 检查配置元素
            const configElements = document.querySelectorAll('[data-config]');
            status += `🔧 配置元素数量: ${configElements.length}\n`;
            
            statusContainer.textContent = status;
        }
        
        function checkDOMElements() {
            const statusContainer = document.getElementById('system-status');
            let status = '';
            
            // 检查关键DOM元素
            const elements = [
                { selector: '.gw-tab[data-tab="configurations"]', name: '配置标签' },
                { selector: '#configurations', name: '配置内容区' },
                { selector: '[data-config]', name: '配置项元素' },
                { selector: '.gw-config-item', name: '配置项容器' }
            ];
            
            elements.forEach(({ selector, name }) => {
                const found = document.querySelectorAll(selector);
                status += `${found.length > 0 ? '✅' : '❌'} ${name}: ${found.length} 个\n`;
            });
            
            statusContainer.textContent = status;
        }
        
        function checkConfigData() {
            const statusContainer = document.getElementById('system-status');
            let status = '';
            
            if (typeof editor !== 'undefined' && editor.config) {
                const configs = editor.config.configurations || {};
                status += '📋 可用配置:\n';
                Object.keys(configs).forEach(configName => {
                    status += `   - ${configName}\n`;
                });
            } else {
                status += '❌ 无法访问配置数据\n';
            }
            
            statusContainer.textContent = status;
        }
        
        function testJumpToConfig() {
            const configName = document.getElementById('config-name').value;
            const statusContainer = document.getElementById('jump-status');
            
            if (!configName) {
                statusContainer.textContent = '❌ 请输入配置名称';
                return;
            }
            
            statusContainer.textContent = `🔗 开始测试跳转到: ${configName}\n`;
            
            if (typeof editor !== 'undefined') {
                try {
                    editor.jumpToConfiguration(configName);
                    statusContainer.textContent += '✅ 跳转函数调用成功\n';
                } catch (error) {
                    statusContainer.textContent += `❌ 跳转函数调用失败: ${error.message}\n`;
                }
            } else {
                statusContainer.textContent += '❌ 编辑器对象不存在\n';
            }
        }
        
        function testSwitchTab() {
            const statusContainer = document.getElementById('jump-status');
            
            if (typeof editor !== 'undefined') {
                try {
                    editor.switchTab('configurations');
                    statusContainer.textContent = '✅ 标签切换函数调用成功\n';
                } catch (error) {
                    statusContainer.textContent = `❌ 标签切换失败: ${error.message}\n`;
                }
            } else {
                statusContainer.textContent = '❌ 编辑器对象不存在\n';
            }
        }
        
        function testScrollToElement() {
            const configName = document.getElementById('config-name').value;
            const statusContainer = document.getElementById('jump-status');
            
            if (typeof editor !== 'undefined') {
                try {
                    editor.scrollToConfiguration(configName);
                    statusContainer.textContent = '✅ 滚动函数调用成功\n';
                } catch (error) {
                    statusContainer.textContent = `❌ 滚动函数调用失败: ${error.message}\n`;
                }
            } else {
                statusContainer.textContent = '❌ 编辑器对象不存在\n';
            }
        }
        
        function clearLogs() {
            logs = [];
            document.getElementById('real-time-logs').textContent = '';
        }
        
        function exportLogs() {
            const logText = logs.map(log => `[${log.timestamp}] ${log.message}`).join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `jump-debug-logs-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function openMainEditor() {
            window.open('/config-editor.html', '_blank');
        }
        
        function simulateModelEdit() {
            // 模拟模型编辑场景
            console.log('🎭 模拟模型编辑场景');
            if (typeof editor !== 'undefined') {
                // 模拟打开模型编辑对话框
                editor.showModal('编辑模型', '<p>模拟的模型编辑表单</p>', () => {});
            }
        }
        
        function simulateConfigClick() {
            // 模拟点击配置链接
            console.log('🎭 模拟配置链接点击');
            const configName = document.getElementById('config-name').value;
            if (configName && typeof editor !== 'undefined') {
                editor.jumpToConfiguration(configName);
            }
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            console.log('🚀 调试页面加载完成');
            checkSystemStatus();
        });
    </script>
</body>
</html>
