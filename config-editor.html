<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gateway 配置编辑器</title>
    <link rel="stylesheet" href="config-editor.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="gw-config-editor">
        <!-- 头部 -->
        <header class="gw-header">
            <div class="gw-header-content">
                <h1><i class="fas fa-cogs"></i> Gateway 配置编辑器</h1>
                <div class="gw-header-actions">
                    <button class="gw-btn gw-btn-secondary" id="gw-load-btn">
                        <i class="fas fa-upload"></i> 加载配置
                    </button>
                    <button class="gw-btn gw-btn-primary" id="gw-save-btn">
                        <i class="fas fa-save"></i> 保存配置
                    </button>
                    <button class="gw-btn gw-btn-danger" id="gw-logout-btn">
                        <i class="fas fa-sign-out-alt"></i> 登出
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="gw-main">
            <!-- 侧边栏导航 -->
            <nav class="gw-sidebar">
                <ul class="gw-nav-list">
                    <li class="gw-nav-item active" data-section="gateway-paths">
                        <i class="fas fa-route"></i> 网关路径
                    </li>
                    <li class="gw-nav-item" data-section="configurations">
                        <i class="fas fa-layer-group"></i> 配置策略
                    </li>
                    <li class="gw-nav-item" data-section="models">
                        <i class="fas fa-robot"></i> 模型配置
                    </li>
                    <li class="gw-nav-item" data-section="modelsmap">
                        <i class="fas fa-exchange-alt"></i> 模型映射
                    </li>
                    <li class="gw-nav-item" data-section="providers">
                        <i class="fas fa-server"></i> 提供商
                    </li>
                </ul>
            </nav>

            <!-- 内容区域 -->
            <div class="gw-content">
                <!-- 网关路径配置 -->
                <section class="gw-section active" id="gateway-paths">
                    <div class="gw-section-header">
                        <h2>网关路径配置</h2>
                        <p class="gw-section-desc">配置可用的网关路径，支持任意字母数字组合</p>
                    </div>
                    <div class="gw-card">
                        <div class="gw-card-header">
                            <h3>路径列表</h3>
                            <button class="gw-btn gw-btn-small gw-btn-primary" id="gw-add-path-btn">
                                <i class="fas fa-plus"></i> 添加路径
                            </button>
                        </div>
                        <div class="gw-card-body">
                            <div class="gw-paths-container" id="gw-paths-container">
                                <!-- 路径项将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 配置策略 -->
                <section class="gw-section" id="configurations">
                    <div class="gw-section-header">
                        <h2>配置策略</h2>
                        <p class="gw-section-desc">管理负载均衡和故障转移策略</p>
                    </div>
                    <div class="gw-card">
                        <div class="gw-card-header">
                            <h3>策略列表</h3>
                            <button class="gw-btn gw-btn-small gw-btn-primary" id="gw-add-config-btn">
                                <i class="fas fa-plus"></i> 添加配置
                            </button>
                        </div>
                        <div class="gw-card-body">
                            <div class="gw-configs-container" id="gw-configs-container">
                                <!-- 配置项将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 模型配置 -->
                <section class="gw-section" id="models">
                    <div class="gw-section-header">
                        <h2>模型配置</h2>
                        <p class="gw-section-desc">配置模型的使用范围和类型</p>
                    </div>
                    <div class="gw-card">
                        <div class="gw-card-header">
                            <h3>模型列表</h3>
                            <div class="gw-search-box">
                                <input type="text" placeholder="搜索模型..." id="gw-model-search">
                                <i class="fas fa-search"></i>
                            </div>
                            <button class="gw-btn gw-btn-small gw-btn-primary" id="gw-add-model-btn">
                                <i class="fas fa-plus"></i> 添加模型
                            </button>
                        </div>
                        <div class="gw-card-body">
                            <div class="gw-models-container" id="gw-models-container">
                                <!-- 模型项将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 模型映射 -->
                <section class="gw-section" id="modelsmap">
                    <div class="gw-section-header">
                        <h2>模型映射</h2>
                        <p class="gw-section-desc">配置模型名称映射关系</p>
                    </div>
                    <div class="gw-card">
                        <div class="gw-card-header">
                            <h3>映射列表</h3>
                            <button class="gw-btn gw-btn-small gw-btn-primary" id="gw-add-mapping-btn">
                                <i class="fas fa-plus"></i> 添加映射
                            </button>
                        </div>
                        <div class="gw-card-body">
                            <div class="gw-mappings-container" id="gw-mappings-container">
                                <!-- 映射项将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 提供商配置 -->
                <section class="gw-section" id="providers">
                    <div class="gw-section-header">
                        <h2>提供商配置</h2>
                        <p class="gw-section-desc">配置API提供商的连接信息</p>
                    </div>
                    <div class="gw-card">
                        <div class="gw-card-header">
                            <h3>提供商列表</h3>
                            <div class="gw-search-box">
                                <input type="text" placeholder="搜索提供商..." id="gw-provider-search">
                                <i class="fas fa-search"></i>
                            </div>
                            <button class="gw-btn gw-btn-small gw-btn-primary" id="gw-add-provider-btn">
                                <i class="fas fa-plus"></i> 添加提供商
                            </button>
                        </div>
                        <div class="gw-card-body">
                            <div class="gw-providers-container" id="gw-providers-container">
                                <!-- 提供商项将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- 模态框 -->
        <div class="gw-modal" id="gw-modal">
            <div class="gw-modal-content">
                <div class="gw-modal-header">
                    <h3 id="gw-modal-title">编辑</h3>
                    <button class="gw-modal-close" id="gw-modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="gw-modal-body" id="gw-modal-body">
                    <!-- 模态框内容将在这里动态生成 -->
                </div>
                <div class="gw-modal-footer">
                    <button class="gw-btn gw-btn-secondary" id="gw-modal-cancel">取消</button>
                    <button class="gw-btn gw-btn-primary" id="gw-modal-save">保存</button>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div class="gw-loading" id="gw-loading">
            <div class="gw-spinner"></div>
            <p>加载中...</p>
        </div>

        <!-- 通知 -->
        <div class="gw-notification" id="gw-notification">
            <div class="gw-notification-content">
                <i class="fas fa-check-circle"></i>
                <span id="gw-notification-text">操作成功</span>
            </div>
        </div>
    </div>

    <script src="config-editor.js"></script>
</body>
</html>
