// 测试附加参数编辑功能
const fs = require('fs');

console.log('🧪 测试附加参数编辑功能');
console.log('================================');

// 创建包含各种附加参数的测试配置
function createParamsTestConfig() {
    return {
        "gateway_paths": ["gateway", "test"],
        "configurations": {
            "override-example": {
                "strategy": { "mode": "loadbalance" },
                "targets": [
                    {
                        "base_provider": "openai",
                        "added_params": {
                            "override_params": {
                                "model": "gpt-4-turbo",
                                "max_tokens": 4000,
                                "temperature": 0.7,
                                "top_p": 0.9,
                                "frequency_penalty": 0.1,
                                "presence_penalty": 0.1
                            }
                        }
                    },
                    "backup-provider"
                ]
            },
            "add-params-example": {
                "strategy": { "mode": "fallback" },
                "targets": [
                    {
                        "base_provider": "anthropic",
                        "added_params": {
                            "add_params": {
                                "custom_id": "request-123",
                                "metadata": {
                                    "source": "web-app",
                                    "version": "1.0",
                                    "user_type": "premium"
                                },
                                "timeout": 30,
                                "retry_count": 3,
                                "debug_mode": true
                            }
                        }
                    }
                ]
            },
            "remove-params-example": {
                "strategy": { "mode": "loadbalance" },
                "targets": [
                    {
                        "base_provider": "google",
                        "added_params": {
                            "remove_params": [
                                "user",
                                "stream",
                                "logit_bias",
                                "logprobs",
                                "top_logprobs"
                            ]
                        }
                    }
                ]
            },
            "complex-params-example": {
                "strategy": { "mode": "fallback" },
                "targets": [
                    {
                        "base_provider": "gemini",
                        "added_params": {
                            "override_params": {
                                "model": "gemini-1.5-flash",
                                "max_tokens": 2000,
                                "temperature": 0.3
                            },
                            "add_params": {
                                "safety_settings": [
                                    {
                                        "category": "HARM_CATEGORY_HARASSMENT",
                                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                                    },
                                    {
                                        "category": "HARM_CATEGORY_HATE_SPEECH",
                                        "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                                    }
                                ],
                                "generation_config": {
                                    "candidate_count": 1,
                                    "stop_sequences": ["END", "STOP"]
                                }
                            },
                            "remove_params": [
                                "stream",
                                "echo"
                            ]
                        }
                    },
                    {
                        "strategy": { "mode": "loadbalance" },
                        "targets": [
                            {
                                "base_provider": "openai-backup",
                                "added_params": {
                                    "override_params": {
                                        "model": "gpt-3.5-turbo",
                                        "max_tokens": 1000
                                    }
                                }
                            },
                            "final-fallback"
                        ]
                    }
                ]
            }
        },
        "models": {
            "advanced-model": {
                "config-name": "complex-params-example",
                "use-in": ["gateway"],
                "type": ["chat"]
            }
        },
        "providers": {
            "openai": {
                "provider": "openai",
                "api_key": "sk-test1"
            },
            "anthropic": {
                "provider": "anthropic",
                "api_key": "sk-test2"
            },
            "google": {
                "provider": "google",
                "api_key": "sk-test3"
            },
            "gemini": {
                "provider": "google",
                "api_key": "sk-gemini"
            }
        }
    };
}

// 验证附加参数结构
function validateAddedParams(params, path = '') {
    const errors = [];
    const info = [];
    
    if (!params || typeof params !== 'object') {
        return { errors, info };
    }
    
    // 检查 override_params
    if (params.override_params) {
        info.push(`${path} 包含覆盖参数: ${Object.keys(params.override_params).length} 个`);
        Object.entries(params.override_params).forEach(([key, value]) => {
            const type = typeof value;
            info.push(`  - ${key}: ${type} = ${JSON.stringify(value)}`);
        });
    }
    
    // 检查 add_params
    if (params.add_params) {
        info.push(`${path} 包含添加参数: ${Object.keys(params.add_params).length} 个`);
        Object.entries(params.add_params).forEach(([key, value]) => {
            const type = Array.isArray(value) ? 'array' : typeof value;
            info.push(`  - ${key}: ${type} = ${JSON.stringify(value).substring(0, 100)}${JSON.stringify(value).length > 100 ? '...' : ''}`);
        });
    }
    
    // 检查 remove_params
    if (params.remove_params) {
        if (!Array.isArray(params.remove_params)) {
            errors.push(`${path} remove_params 应该是数组格式`);
        } else {
            info.push(`${path} 包含移除参数: ${params.remove_params.length} 个`);
            params.remove_params.forEach(param => {
                info.push(`  - 移除: ${param}`);
            });
        }
    }
    
    return { errors, info };
}

// 递归检查配置中的所有附加参数
function checkAllAddedParams(config) {
    console.log('\n📋 检查所有附加参数...');
    
    let totalErrors = [];
    let totalInfo = [];
    
    if (config.configurations) {
        Object.entries(config.configurations).forEach(([configName, configValue]) => {
            if (typeof configValue === 'object' && configValue.targets) {
                checkTargetsParams(configValue.targets, configName, 0, totalErrors, totalInfo);
            }
        });
    }
    
    return { errors: totalErrors, info: totalInfo };
}

// 递归检查targets中的参数
function checkTargetsParams(targets, configName, level, errors, info) {
    if (!Array.isArray(targets)) return;
    
    targets.forEach((target, index) => {
        const path = `${configName}[${level}][${index}]`;
        
        if (typeof target === 'object' && target.added_params) {
            const result = validateAddedParams(target.added_params, path);
            errors.push(...result.errors);
            info.push(...result.info);
        }
        
        // 递归检查嵌套targets
        if (typeof target === 'object' && target.targets) {
            checkTargetsParams(target.targets, configName, level + 1, errors, info);
        }
    });
}

// 测试参数操作
function testParamOperations() {
    console.log('\n🔧 测试参数操作...');
    
    const config = createParamsTestConfig();
    
    // 测试1: 添加新的覆盖参数
    console.log('测试1: 添加覆盖参数');
    const target1 = config.configurations['override-example'].targets[0];
    target1.added_params.override_params.stop = ["\\n", "END"];
    target1.added_params.override_params.seed = 12345;
    console.log('✅ 成功添加覆盖参数');
    
    // 测试2: 添加新的参数组
    console.log('测试2: 添加新的参数组');
    const target2 = config.configurations['add-params-example'].targets[0];
    target2.added_params.override_params = {
        "model": "claude-3-sonnet",
        "max_tokens": 3000
    };
    console.log('✅ 成功添加新的参数组');
    
    // 测试3: 修改移除参数
    console.log('测试3: 修改移除参数');
    const target3 = config.configurations['remove-params-example'].targets[0];
    target3.added_params.remove_params.push('functions');
    target3.added_params.remove_params.push('function_call');
    console.log('✅ 成功修改移除参数');
    
    // 测试4: 删除参数
    console.log('测试4: 删除参数');
    delete target1.added_params.override_params.frequency_penalty;
    console.log('✅ 成功删除参数');
    
    return config;
}

// 生成参数统计报告
function generateParamsReport(config) {
    console.log('\n📊 参数统计报告');
    console.log('================');
    
    let overrideCount = 0;
    let addCount = 0;
    let removeCount = 0;
    let totalParams = 0;
    
    const result = checkAllAddedParams(config);
    
    result.info.forEach(info => {
        if (info.includes('覆盖参数')) {
            overrideCount++;
        } else if (info.includes('添加参数')) {
            addCount++;
        } else if (info.includes('移除参数')) {
            removeCount++;
        }
        
        const match = info.match(/(\d+) 个/);
        if (match) {
            totalParams += parseInt(match[1]);
        }
    });
    
    console.log(`📈 统计信息:`);
    console.log(`   - 覆盖参数组: ${overrideCount} 个`);
    console.log(`   - 添加参数组: ${addCount} 个`);
    console.log(`   - 移除参数组: ${removeCount} 个`);
    console.log(`   - 总参数数量: ${totalParams} 个`);
    
    if (result.errors.length > 0) {
        console.log(`❌ 发现错误: ${result.errors.length} 个`);
        result.errors.forEach(error => console.log(`   - ${error}`));
    } else {
        console.log(`✅ 所有参数配置正确`);
    }
    
    return {
        overrideCount,
        addCount,
        removeCount,
        totalParams,
        errors: result.errors.length
    };
}

// 主测试函数
async function runParamsTests() {
    try {
        console.log('1. 创建参数测试配置...');
        const testConfig = createParamsTestConfig();
        
        console.log('2. 验证原始参数配置...');
        const originalReport = generateParamsReport(testConfig);
        
        console.log('3. 测试参数操作...');
        const modifiedConfig = testParamOperations();
        
        console.log('4. 验证修改后的参数配置...');
        const modifiedReport = generateParamsReport(modifiedConfig);
        
        console.log('5. 保存测试配置...');
        fs.writeFileSync('./test-params-config.json', JSON.stringify(modifiedConfig, null, 2));
        console.log('✅ 参数测试配置已保存到 test-params-config.json');
        
        console.log('\n🎉 参数编辑功能测试完成！');
        console.log('\n📋 测试对比:');
        console.log(`   修改前 - 覆盖参数组: ${originalReport.overrideCount}, 总参数: ${originalReport.totalParams}`);
        console.log(`   修改后 - 覆盖参数组: ${modifiedReport.overrideCount}, 总参数: ${modifiedReport.totalParams}`);
        
        console.log('\n🚀 使用建议:');
        console.log('1. 启动编辑器: npm start');
        console.log('2. 登录系统: http://localhost:3474/login.html');
        console.log('3. 查看"配置策略"部分的附加参数');
        console.log('4. 尝试可视化编辑参数功能');
        console.log('5. 测试添加、删除、修改参数操作');
        
        console.log('\n💡 功能特色:');
        console.log('- ✅ 可视化参数编辑器');
        console.log('- ✅ 支持覆盖、添加、移除三种参数类型');
        console.log('- ✅ 自动类型识别和转换');
        console.log('- ✅ 高级JSON编辑模式');
        console.log('- ✅ 实时参数验证');
        
    } catch (error) {
        console.log('❌ 参数测试失败:', error.message);
        console.log('\n🔧 建议:');
        console.log('1. 检查JSON格式是否正确');
        console.log('2. 确保有文件写入权限');
        console.log('3. 查看详细错误信息进行调试');
    }
}

// 运行测试
runParamsTests();
