// 测试配置项附加参数编辑功能
const fs = require('fs');

console.log('🧪 测试配置项附加参数编辑功能');
console.log('====================================');

// 创建包含配置项附加参数的测试配置
function createConfigParamsTestConfig() {
    return {
        "gateway_paths": ["gateway", "test", "api"],
        "configurations": {
            // 简单配置（字符串引用）
            "simple-config": "openai-provider",
            
            // 只有基础提供商的配置
            "base-provider-config": {
                "base_provider": "vertex-ai"
            },
            
            // 带附加参数的基础配置
            "params-config-1": {
                "base_provider": "vertex-ai",
                "added_params": {
                    "override_params": {
                        "max_tokens": 8192,
                        "model": "gemini-1.5-flash-002",
                        "temperature": 0.7
                    }
                }
            },
            
            // 复杂配置：基础提供商 + 附加参数
            "params-config-2": {
                "base_provider": "openai",
                "added_params": {
                    "override_params": {
                        "model": "gpt-4-turbo",
                        "max_tokens": 4000
                    },
                    "add_params": {
                        "custom_id": "config-request",
                        "metadata": {
                            "source": "config-level",
                            "priority": "high"
                        }
                    },
                    "remove_params": [
                        "stream",
                        "echo"
                    ]
                }
            },
            
            // 策略配置 + 附加参数
            "strategy-with-params": {
                "strategy": { "mode": "loadbalance" },
                "targets": ["provider1", "provider2"],
                "added_params": {
                    "override_params": {
                        "timeout": 30,
                        "retry_count": 3
                    }
                }
            },
            
            // 完整复杂配置
            "full-complex-config": {
                "base_provider": "anthropic",
                "strategy": { "mode": "fallback" },
                "retry": { "attempts": 2 },
                "targets": [
                    {
                        "base_provider": "openai-backup",
                        "added_params": {
                            "override_params": {
                                "model": "gpt-3.5-turbo"
                            }
                        }
                    },
                    "final-fallback"
                ],
                "added_params": {
                    "override_params": {
                        "model": "claude-3-sonnet",
                        "max_tokens": 3000
                    },
                    "add_params": {
                        "safety_level": "strict",
                        "content_filter": true
                    }
                }
            }
        },
        "models": {
            "test-model-1": {
                "config-name": "params-config-1",
                "use-in": ["gateway"],
                "type": ["chat"]
            },
            "test-model-2": {
                "config-name": "params-config-2",
                "use-in": ["api"],
                "type": ["chat"]
            }
        },
        "providers": {
            "vertex-ai": {
                "provider": "google",
                "api_key": "sk-vertex"
            },
            "openai": {
                "provider": "openai",
                "api_key": "sk-openai"
            },
            "anthropic": {
                "provider": "anthropic",
                "api_key": "sk-anthropic"
            }
        }
    };
}

// 验证配置项的附加参数
function validateConfigParams(config) {
    console.log('\n📋 验证配置项附加参数...');
    
    const results = {
        total: 0,
        withParams: 0,
        overrideParams: 0,
        addParams: 0,
        removeParams: 0,
        errors: []
    };
    
    Object.entries(config.configurations).forEach(([configName, configValue]) => {
        results.total++;
        
        if (typeof configValue === 'string') {
            console.log(`✅ ${configName}: 简单配置 -> ${configValue}`);
            return;
        }
        
        if (typeof configValue === 'object') {
            console.log(`🔧 ${configName}: 复杂配置`);
            
            // 检查基础提供商
            if (configValue.base_provider) {
                console.log(`   📡 基础提供商: ${configValue.base_provider}`);
            }
            
            // 检查策略
            if (configValue.strategy) {
                console.log(`   📋 策略: ${configValue.strategy.mode}`);
            }
            
            // 检查目标
            if (configValue.targets) {
                console.log(`   🎯 目标数量: ${configValue.targets.length}`);
            }
            
            // 检查附加参数
            if (configValue.added_params) {
                results.withParams++;
                console.log(`   ⚙️  附加参数:`);
                
                if (configValue.added_params.override_params) {
                    results.overrideParams++;
                    const overrideCount = Object.keys(configValue.added_params.override_params).length;
                    console.log(`      🔧 覆盖参数: ${overrideCount} 个`);
                    Object.entries(configValue.added_params.override_params).forEach(([key, value]) => {
                        console.log(`         - ${key}: ${JSON.stringify(value)}`);
                    });
                }
                
                if (configValue.added_params.add_params) {
                    results.addParams++;
                    const addCount = Object.keys(configValue.added_params.add_params).length;
                    console.log(`      ➕ 添加参数: ${addCount} 个`);
                    Object.entries(configValue.added_params.add_params).forEach(([key, value]) => {
                        console.log(`         - ${key}: ${JSON.stringify(value)}`);
                    });
                }
                
                if (configValue.added_params.remove_params) {
                    results.removeParams++;
                    console.log(`      ➖ 移除参数: ${configValue.added_params.remove_params.length} 个`);
                    configValue.added_params.remove_params.forEach(param => {
                        console.log(`         - ${param}`);
                    });
                }
            } else {
                console.log(`   ⚙️  无附加参数`);
            }
        }
    });
    
    return results;
}

// 测试配置参数操作
function testConfigParamOperations() {
    console.log('\n🔧 测试配置参数操作...');
    
    const config = createConfigParamsTestConfig();
    
    // 测试1: 为现有配置添加附加参数
    console.log('测试1: 为基础配置添加附加参数');
    config.configurations['base-provider-config'].added_params = {
        "override_params": {
            "model": "gemini-1.5-pro",
            "max_tokens": 4096
        }
    };
    console.log('✅ 成功添加附加参数');
    
    // 测试2: 修改现有附加参数
    console.log('测试2: 修改现有附加参数');
    config.configurations['params-config-1'].added_params.override_params.temperature = 0.9;
    config.configurations['params-config-1'].added_params.override_params.top_p = 0.95;
    console.log('✅ 成功修改附加参数');
    
    // 测试3: 添加新的参数类型
    console.log('测试3: 添加新的参数类型');
    config.configurations['params-config-1'].added_params.add_params = {
        "request_id": "test-123",
        "priority": "high"
    };
    console.log('✅ 成功添加新参数类型');
    
    // 测试4: 删除参数
    console.log('测试4: 删除参数');
    delete config.configurations['params-config-2'].added_params.remove_params;
    console.log('✅ 成功删除参数类型');
    
    return config;
}

// 生成配置参数报告
function generateConfigParamsReport(config) {
    console.log('\n📊 配置参数统计报告');
    console.log('====================');
    
    const results = validateConfigParams(config);
    
    console.log(`\n📈 统计信息:`);
    console.log(`   - 总配置数量: ${results.total}`);
    console.log(`   - 包含附加参数: ${results.withParams}`);
    console.log(`   - 覆盖参数配置: ${results.overrideParams}`);
    console.log(`   - 添加参数配置: ${results.addParams}`);
    console.log(`   - 移除参数配置: ${results.removeParams}`);
    
    if (results.errors.length > 0) {
        console.log(`❌ 发现错误: ${results.errors.length} 个`);
        results.errors.forEach(error => console.log(`   - ${error}`));
    } else {
        console.log(`✅ 所有配置参数正确`);
    }
    
    return results;
}

// 主测试函数
async function runConfigParamsTests() {
    try {
        console.log('1. 创建配置参数测试配置...');
        const testConfig = createConfigParamsTestConfig();
        
        console.log('2. 验证原始配置参数...');
        const originalReport = generateConfigParamsReport(testConfig);
        
        console.log('3. 测试配置参数操作...');
        const modifiedConfig = testConfigParamOperations();
        
        console.log('4. 验证修改后的配置参数...');
        const modifiedReport = generateConfigParamsReport(modifiedConfig);
        
        console.log('5. 保存测试配置...');
        fs.writeFileSync('./test-config-params.json', JSON.stringify(modifiedConfig, null, 2));
        console.log('✅ 配置参数测试配置已保存到 test-config-params.json');
        
        console.log('\n🎉 配置项附加参数功能测试完成！');
        console.log('\n📋 测试对比:');
        console.log(`   修改前 - 包含参数配置: ${originalReport.withParams}/${originalReport.total}`);
        console.log(`   修改后 - 包含参数配置: ${modifiedReport.withParams}/${modifiedReport.total}`);
        
        console.log('\n🚀 使用建议:');
        console.log('1. 启动编辑器: npm start');
        console.log('2. 登录系统: http://localhost:3474/login.html');
        console.log('3. 进入"配置策略"部分');
        console.log('4. 点击配置项的"编辑"按钮');
        console.log('5. 在弹出的对话框中编辑附加参数');
        
        console.log('\n💡 功能特色:');
        console.log('- ✅ 配置项级别的附加参数编辑');
        console.log('- ✅ 可视化参数显示和编辑');
        console.log('- ✅ 支持基础提供商配置');
        console.log('- ✅ 完整的参数类型支持');
        console.log('- ✅ 实时参数验证和保存');
        
    } catch (error) {
        console.log('❌ 配置参数测试失败:', error.message);
        console.log('\n🔧 建议:');
        console.log('1. 检查JSON格式是否正确');
        console.log('2. 确保有文件写入权限');
        console.log('3. 查看详细错误信息进行调试');
    }
}

// 运行测试
runConfigParamsTests();
