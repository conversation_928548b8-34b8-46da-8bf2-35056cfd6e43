@echo off
chcp 65001 >nul
cls

echo ===================================
echo   Gateway Config Editor
echo ===================================
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

echo ✅ Node.js 版本:
node --version

REM 检查npm是否安装
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装，请先安装 npm
    pause
    exit /b 1
)

echo ✅ npm 版本:
npm --version

REM 检查配置文件是否存在
set CONFIG_FILE=C:\Users\<USER>\Downloads\138.2.119.225\202507281503\conf.json
if not exist "%CONFIG_FILE%" (
    echo ⚠️  配置文件不存在: %CONFIG_FILE%
    echo    编辑器将使用示例配置运行
) else (
    echo ✅ 配置文件存在: %CONFIG_FILE%
)

REM 检查依赖是否安装
if not exist "node_modules" (
    echo.
    echo 📦 安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
)

REM 检查端口是否被占用
set PORT=3474
netstat -an | find ":%PORT%" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口 %PORT% 已被占用，请手动关闭占用进程
    echo    或者修改 config-api.js 中的端口号
)

echo.
echo 🚀 启动配置编辑器...
echo    端口: %PORT%
echo    登录页面: http://localhost:%PORT%/login.html
echo    编辑器页面: http://localhost:%PORT%/config-editor.html
echo    演示页面: http://localhost:%PORT%/demo.html
echo    默认密码: admin123 (可在 .env 文件中修改)
echo.
echo 按 Ctrl+C 停止服务器
echo.

REM 启动服务器
node config-api.js

pause
