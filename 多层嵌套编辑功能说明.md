# 多层嵌套配置编辑功能说明

## 🎯 功能概述

现在的配置编辑器完全支持多层嵌套配置的**查看、编辑、添加和删除**操作，可以处理任意复杂的配置结构。

## 🔧 支持的操作

### 1. 查看嵌套结构
- ✅ **层级显示**: 清晰的缩进和颜色区分不同层级
- ✅ **展开/折叠**: 复杂配置可以展开查看详情
- ✅ **类型识别**: 自动识别简单字符串和复杂对象目标

### 2. 编辑现有目标
- ✅ **简单目标**: 直接在输入框中修改提供商名称
- ✅ **复杂目标**: 点击编辑按钮修改策略、提供商、参数
- ✅ **实时更新**: 修改后立即生效

### 3. 添加新目标
- ✅ **简单目标**: 添加提供商名称字符串
- ✅ **复杂目标**: 添加包含策略、提供商、参数的对象
- ✅ **灵活配置**: 可选择包含哪些配置项

### 4. 删除目标
- ✅ **安全删除**: 确认对话框防止误删
- ✅ **任意层级**: 支持删除任意层级的目标
- ✅ **自动更新**: 删除后自动刷新显示

### 5. 拖拽排序
- ✅ **fallback模式**: 自动检测并启用拖拽排序
- ✅ **多层支持**: 每个层级独立支持拖拽
- ✅ **视觉反馈**: 拖拽时的视觉提示

## 📋 操作指南

### 查看嵌套配置
1. 进入"配置策略"部分
2. 找到包含嵌套结构的配置项
3. 点击配置项展开查看详情
4. 点击复杂目标的展开按钮查看子配置

### 编辑简单目标
1. 找到要编辑的简单目标（显示为输入框）
2. 直接点击输入框进行编辑
3. 输入新的提供商名称
4. 点击其他地方或按回车保存

### 编辑复杂目标
1. 找到要编辑的复杂目标
2. 点击"编辑"按钮（铅笔图标）
3. 在弹出的对话框中修改配置：
   - **策略配置**: 选择是否包含策略，设置模式
   - **基础提供商**: 选择是否包含基础提供商
   - **附加参数**: 选择是否包含参数，编辑JSON
4. 点击"保存"确认修改

### 添加新目标
1. 在目标列表顶部点击"添加目标"按钮
2. 选择目标类型：
   - **简单目标**: 输入提供商名称
   - **复杂目标**: 配置策略、提供商、参数
3. 填写相应信息
4. 点击"保存"添加目标

### 删除目标
1. 找到要删除的目标
2. 点击"删除"按钮（垃圾桶图标）
3. 在确认对话框中点击"确定"

### 拖拽排序（仅fallback模式）
1. 确认配置的策略模式为"fallback"
2. 鼠标悬停在目标项上，显示拖拽手柄
3. 按住拖拽手柄拖动目标到新位置
4. 释放鼠标完成排序

## 🎨 界面说明

### 视觉元素
- **🔗 拖拽手柄**: 六个点的图标，仅在fallback模式显示
- **🖥️ 服务器图标**: 简单目标的标识
- **📚 层级图标**: 复杂目标的标识
- **⬇️ 展开箭头**: 点击展开/折叠复杂配置
- **✏️ 编辑按钮**: 编辑复杂目标配置
- **🗑️ 删除按钮**: 删除目标

### 颜色编码
- **蓝色边框**: 第一层嵌套
- **绿色边框**: 第二层嵌套  
- **橙色边框**: 第三层嵌套
- **灰色背景**: 普通状态
- **蓝色背景**: 悬停状态

## 📝 配置示例

### 简单配置
```json
{
  "strategy": { "mode": "loadbalance" },
  "targets": ["provider1", "provider2", "provider3"]
}
```

### 复杂嵌套配置
```json
{
  "strategy": { "mode": "fallback" },
  "targets": [
    {
      "strategy": { "mode": "loadbalance" },
      "targets": ["fast1", "fast2", "fast3"]
    },
    {
      "strategy": { "mode": "fallback" },
      "targets": [
        {
          "strategy": { "mode": "loadbalance" },
          "targets": ["backup1", "backup2"]
        },
        "final-backup"
      ]
    },
    {
      "base_provider": "gemini",
      "added_params": {
        "override_params": {
          "model": "gemini-1.5-flash"
        }
      }
    }
  ]
}
```

## ⚠️ 注意事项

### 编辑限制
1. **JSON格式**: 附加参数必须是有效的JSON格式
2. **策略模式**: 只有"loadbalance"和"fallback"两种模式
3. **提供商名称**: 必须是有效的提供商标识符

### 最佳实践
1. **备份配置**: 编辑前建议备份当前配置
2. **逐步修改**: 复杂配置建议分步骤修改
3. **测试验证**: 修改后测试配置是否正常工作
4. **合理嵌套**: 避免过深的嵌套层级（建议不超过3层）

## 🔍 故障排除

### 常见问题
1. **拖拽不工作**: 检查策略模式是否为"fallback"
2. **JSON错误**: 检查附加参数的JSON格式是否正确
3. **保存失败**: 检查必填字段是否已填写
4. **显示异常**: 刷新页面重新加载配置

### 错误提示
- **"JSON格式错误"**: 附加参数不是有效的JSON
- **"保存失败"**: 数据验证失败或网络错误
- **"配置加载失败"**: 无法读取配置文件

## 🎉 功能优势

1. **完整支持**: 支持查看、编辑、添加、删除所有操作
2. **直观操作**: 可视化界面，操作简单直观
3. **实时反馈**: 修改后立即显示效果
4. **安全可靠**: 确认对话框和数据验证
5. **灵活配置**: 支持任意复杂的嵌套结构

现在您可以完全通过可视化界面管理复杂的多层嵌套配置，无需手动编辑JSON文件！
