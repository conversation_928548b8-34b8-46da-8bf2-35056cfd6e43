<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳转功能调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 跳转功能调试页面</h1>
        
        <div class="debug-section">
            <div class="debug-title">📊 系统状态检查</div>
            <button class="test-button" onclick="checkSystemStatus()">检查系统状态</button>
            <button class="test-button" onclick="loadTestConfig()">加载测试配置</button>
            <div id="system-status"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🧪 跳转功能测试</div>
            <button class="test-button" onclick="testConfigJump()">测试配置跳转</button>
            <button class="test-button" onclick="testModelJump()">测试模型跳转</button>
            <button class="test-button" onclick="testMappingJump()">测试映射跳转</button>
            <div id="jump-test-results"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🔍 DOM 元素检查</div>
            <button class="test-button" onclick="checkDOMElements()">检查DOM元素</button>
            <button class="test-button" onclick="checkEventBindings()">检查事件绑定</button>
            <div id="dom-check-results"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">📝 实时日志</div>
            <button class="test-button" onclick="clearLogs()">清空日志</button>
            <button class="test-button" onclick="openMainEditor()">打开主编辑器</button>
            <div id="log-area" class="log-area"></div>
        </div>
    </div>

    <script>
        let logArea = null;
        
        function log(message, type = 'info') {
            if (!logArea) {
                logArea = document.getElementById('log-area');
            }
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('log-area').textContent = '';
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function checkSystemStatus() {
            log('🔍 开始检查系统状态...');
            
            try {
                // 检查API连接
                const response = await fetch('/api/config');
                if (response.ok) {
                    const config = await response.json();
                    log('✅ API连接正常');
                    log(`📊 配置数据: ${Object.keys(config.configurations || {}).length} 个配置, ${Object.keys(config.models || {}).length} 个模型`);
                    showStatus('system-status', '✅ 系统状态正常', 'success');
                } else {
                    log('❌ API连接失败: ' + response.status);
                    showStatus('system-status', '❌ API连接失败', 'error');
                }
            } catch (error) {
                log('❌ 系统检查失败: ' + error.message);
                showStatus('system-status', '❌ 系统检查失败: ' + error.message, 'error');
            }
        }

        async function loadTestConfig() {
            log('📥 加载测试配置...');
            
            try {
                const response = await fetch('/test-jump-config.json');
                if (response.ok) {
                    const testConfig = await response.json();
                    
                    // 上传测试配置
                    const uploadResponse = await fetch('/api/config', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(testConfig)
                    });
                    
                    if (uploadResponse.ok) {
                        log('✅ 测试配置加载成功');
                        showStatus('system-status', '✅ 测试配置已加载', 'success');
                    } else {
                        log('❌ 测试配置上传失败');
                        showStatus('system-status', '❌ 测试配置上传失败', 'error');
                    }
                } else {
                    log('❌ 测试配置文件不存在');
                    showStatus('system-status', '❌ 测试配置文件不存在', 'error');
                }
            } catch (error) {
                log('❌ 加载测试配置失败: ' + error.message);
                showStatus('system-status', '❌ 加载测试配置失败', 'error');
            }
        }

        function testConfigJump() {
            log('🔗 测试配置跳转功能...');
            
            // 模拟跳转到配置
            const testConfigName = 'openai-config';
            log(`🎯 模拟跳转到配置: ${testConfigName}`);
            
            // 这里应该调用主编辑器的跳转功能
            // 由于是在独立页面，我们只能检查逻辑
            showStatus('jump-test-results', '🔗 配置跳转测试需要在主编辑器中进行', 'info');
        }

        function testModelJump() {
            log('🤖 测试模型跳转功能...');
            showStatus('jump-test-results', '🤖 模型跳转测试需要在主编辑器中进行', 'info');
        }

        function testMappingJump() {
            log('🔄 测试映射跳转功能...');
            showStatus('jump-test-results', '🔄 映射跳转测试需要在主编辑器中进行', 'info');
        }

        function checkDOMElements() {
            log('🔍 检查DOM元素...');
            
            // 这个函数需要在主编辑器页面中运行
            showStatus('dom-check-results', '🔍 DOM检查需要在主编辑器页面中进行', 'info');
        }

        function checkEventBindings() {
            log('🔗 检查事件绑定...');
            showStatus('dom-check-results', '🔗 事件绑定检查需要在主编辑器页面中进行', 'info');
        }

        function openMainEditor() {
            log('🚀 打开主编辑器...');
            window.open('/config-editor.html', '_blank');
        }

        // 页面加载时自动检查系统状态
        window.addEventListener('load', () => {
            log('🚀 调试页面已加载');
            checkSystemStatus();
        });
    </script>
</body>
</html>
