const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const cors = require('cors');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3474;

// 环境变量配置
const LOGIN_PASSWORD = process.env.LOGIN_PASSWORD || 'admin123';
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const SESSION_TIMEOUT = parseInt(process.env.SESSION_TIMEOUT) || 60; // 分钟
const ENABLE_AUTH = process.env.ENABLE_AUTH !== 'false';

// 自动检测配置文件路径
let CONFIG_PATH = process.env.CONFIG_PATH || '/root/test/gateway/conf.json';
const localConfigPath = path.join(__dirname, 'conf.json');

// 如果当前目录有conf.json，优先使用
if (require('fs').existsSync(localConfigPath)) {
    CONFIG_PATH = localConfigPath;
    console.log('Using local config file:', CONFIG_PATH);
} else if (require('fs').existsSync('/root/test/gateway/conf.json')) {
    CONFIG_PATH = '/root/test/gateway/conf.json';
    console.log('Using default config file:', CONFIG_PATH);
} else {
    console.warn('Config file not found, will use sample data');
}

// 中间件
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// 认证中间件
const authenticateToken = (req, res, next) => {
    if (!ENABLE_AUTH) {
        return next();
    }

    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
};

// 静态文件服务
app.get('/', (req, res) => {
    console.log('📍 访问根路径，重定向到登录页面');
    res.redirect('/login.html');
});

// 添加静态文件访问日志
app.use((req, res, next) => {
    if (req.method === 'GET' && !req.path.startsWith('/api/')) {
        console.log(`📁 静态文件请求: ${req.method} ${req.path}`);
    }
    next();
});

app.use(express.static('.'));

// 登录路由
app.post('/api/login', async (req, res) => {
    try {
        const { password } = req.body;

        if (!password) {
            return res.status(400).json({ error: '密码不能为空' });
        }

        if (password !== LOGIN_PASSWORD) {
            return res.status(401).json({ error: '密码错误' });
        }

        // 生成JWT token
        const token = jwt.sign(
            {
                user: 'admin',
                timestamp: Date.now()
            },
            JWT_SECRET,
            { expiresIn: `${SESSION_TIMEOUT}m` }
        );

        console.log('User logged in successfully');
        res.json({
            success: true,
            token,
            message: '登录成功'
        });
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: '登录失败' });
    }
});

// 验证认证状态
app.get('/api/verify-auth', authenticateToken, (req, res) => {
    res.json({
        success: true,
        user: req.user,
        message: '认证有效'
    });
});

// 登出路由
app.post('/api/logout', (req, res) => {
    res.json({ success: true, message: '登出成功' });
});

// 读取配置文件
app.get('/api/config', authenticateToken, async (req, res) => {
    try {
        console.log('🔧 API请求: 获取配置文件 from:', CONFIG_PATH);
        const data = await fs.readFile(CONFIG_PATH, 'utf8');
        const config = JSON.parse(data);

        console.log('✅ 配置文件读取成功');
        console.log('   - 配置数量:', Object.keys(config.configurations || {}).length);
        console.log('   - 模型数量:', Object.keys(config.models || {}).length);
        console.log('   - 映射数量:', Object.keys(config.modelsmap || {}).length);

        res.json(config);
    } catch (error) {
        console.error('❌ 读取配置文件失败:', error);
        res.status(500).json({
            error: 'Failed to read configuration file',
            details: error.message
        });
    }
});

// 保存配置文件
app.post('/api/config', authenticateToken, async (req, res) => {
    try {
        const config = req.body;
        
        // 验证配置格式
        if (!config || typeof config !== 'object') {
            return res.status(400).json({ error: 'Invalid configuration format' });
        }
        
        // 创建备份
        const backupPath = CONFIG_PATH + '.backup.' + Date.now();
        try {
            const existingData = await fs.readFile(CONFIG_PATH, 'utf8');
            await fs.writeFile(backupPath, existingData);
            console.log('Backup created:', backupPath);
        } catch (backupError) {
            console.warn('Could not create backup:', backupError.message);
        }
        
        console.log('💾 API请求: 保存配置文件');
        console.log('📊 配置统计:');
        console.log('   - 配置数量:', Object.keys(config.configurations || {}).length);
        console.log('   - 模型数量:', Object.keys(config.models || {}).length);
        console.log('   - 映射数量:', Object.keys(config.modelsmap || {}).length);

        // 保存新配置
        const configJson = JSON.stringify(config, null, 2);
        await fs.writeFile(CONFIG_PATH, configJson);

        console.log('✅ 配置文件保存成功');
        res.json({ success: true, message: 'Configuration saved successfully' });
    } catch (error) {
        console.error('Error saving config:', error);
        res.status(500).json({ 
            error: 'Failed to save configuration file',
            details: error.message 
        });
    }
});

// 获取配置文件备份列表
app.get('/api/config/backups', authenticateToken, async (req, res) => {
    try {
        const configDir = path.dirname(CONFIG_PATH);
        const files = await fs.readdir(configDir);
        
        const backups = files
            .filter(file => file.startsWith('conf.json.backup.'))
            .map(file => {
                const timestamp = file.split('.').pop();
                return {
                    filename: file,
                    timestamp: parseInt(timestamp),
                    date: new Date(parseInt(timestamp)).toLocaleString('zh-CN')
                };
            })
            .sort((a, b) => b.timestamp - a.timestamp);
        
        res.json(backups);
    } catch (error) {
        console.error('Error reading backups:', error);
        res.status(500).json({ 
            error: 'Failed to read backup files',
            details: error.message 
        });
    }
});

// 恢复备份
app.post('/api/config/restore/:filename', authenticateToken, async (req, res) => {
    try {
        const { filename } = req.params;
        const backupPath = path.join(path.dirname(CONFIG_PATH), filename);
        
        // 验证备份文件存在
        await fs.access(backupPath);
        
        // 读取备份内容
        const backupData = await fs.readFile(backupPath, 'utf8');
        
        // 验证JSON格式
        JSON.parse(backupData);
        
        // 创建当前配置的备份
        const currentBackupPath = CONFIG_PATH + '.backup.' + Date.now();
        try {
            const currentData = await fs.readFile(CONFIG_PATH, 'utf8');
            await fs.writeFile(currentBackupPath, currentData);
        } catch (backupError) {
            console.warn('Could not backup current config:', backupError.message);
        }
        
        // 恢复备份
        await fs.writeFile(CONFIG_PATH, backupData);
        
        console.log('Config restored from backup:', filename);
        res.json({ success: true, message: 'Configuration restored successfully' });
    } catch (error) {
        console.error('Error restoring backup:', error);
        res.status(500).json({ 
            error: 'Failed to restore backup',
            details: error.message 
        });
    }
});

// 验证配置文件
app.post('/api/config/validate', authenticateToken, async (req, res) => {
    try {
        const config = req.body;
        const errors = [];
        const warnings = [];
        
        // 基本结构验证
        if (!config.gateway_paths || !Array.isArray(config.gateway_paths)) {
            errors.push('gateway_paths must be an array');
        }
        
        if (!config.models || typeof config.models !== 'object') {
            errors.push('models must be an object');
        }
        
        if (!config.configurations || typeof config.configurations !== 'object') {
            errors.push('configurations must be an object');
        }
        
        if (!config.providers || typeof config.providers !== 'object') {
            errors.push('providers must be an object');
        }
        
        // 模型配置验证
        if (config.models) {
            Object.entries(config.models).forEach(([modelName, modelConfig]) => {
                if (!modelConfig['config-name']) {
                    errors.push(`Model ${modelName} missing config-name`);
                }
                
                if (modelConfig['use-in'] && !Array.isArray(modelConfig['use-in'])) {
                    errors.push(`Model ${modelName} use-in must be an array`);
                }
                
                if (modelConfig.type && !Array.isArray(modelConfig.type)) {
                    errors.push(`Model ${modelName} type must be an array`);
                }
                
                // 检查use-in中的路径是否在gateway_paths中定义
                if (modelConfig['use-in'] && config.gateway_paths) {
                    modelConfig['use-in'].forEach(path => {
                        if (!config.gateway_paths.includes(path)) {
                            warnings.push(`Model ${modelName} uses undefined path: ${path}`);
                        }
                    });
                }
                
                // 检查config-name是否存在
                if (modelConfig['config-name'] && !config.configurations[modelConfig['config-name']]) {
                    errors.push(`Model ${modelName} references undefined configuration: ${modelConfig['config-name']}`);
                }
            });
        }
        
        // 配置引用验证
        if (config.configurations) {
            Object.entries(config.configurations).forEach(([configName, configValue]) => {
                if (typeof configValue === 'string') {
                    // 简单引用，检查提供商是否存在
                    if (!config.providers[configValue]) {
                        errors.push(`Configuration ${configName} references undefined provider: ${configValue}`);
                    }
                } else if (typeof configValue === 'object') {
                    // 复杂配置验证
                    if (configValue.base_provider && !config.providers[configValue.base_provider]) {
                        errors.push(`Configuration ${configName} references undefined base_provider: ${configValue.base_provider}`);
                    }
                }
            });
        }
        
        res.json({
            valid: errors.length === 0,
            errors,
            warnings
        });
    } catch (error) {
        console.error('Error validating config:', error);
        res.status(500).json({ 
            error: 'Failed to validate configuration',
            details: error.message 
        });
    }
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({ 
        error: 'Internal server error',
        details: error.message 
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`Config API server running on port ${PORT}`);
    console.log(`Config file path: ${CONFIG_PATH}`);
    console.log(`Access the editor at: http://localhost:${PORT}/config-editor.html`);
});

module.exports = app;
