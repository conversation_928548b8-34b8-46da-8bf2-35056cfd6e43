// 测试跳转功能
const fs = require('fs');

console.log('🧪 测试跳转功能');
console.log('=================');

// 创建包含跳转关系的测试配置
function createJumpTestConfig() {
    return {
        "gateway_paths": ["gateway", "api", "test"],
        "configurations": {
            "openai-config": {
                "base_provider": "openai-main",
                "added_params": {
                    "override_params": {
                        "model": "gpt-4-turbo",
                        "max_tokens": 4000
                    }
                }
            },
            "anthropic-config": {
                "strategy": { "mode": "loadbalance" },
                "targets": ["anthropic-1", "anthropic-2"]
            },
            "fallback-config": {
                "strategy": { "mode": "fallback" },
                "targets": [
                    {
                        "base_provider": "primary-provider",
                        "added_params": {
                            "override_params": {
                                "model": "gpt-4"
                            }
                        }
                    },
                    "backup-provider"
                ]
            },
            "gemini-config": {
                "base_provider": "google-vertex",
                "added_params": {
                    "override_params": {
                        "model": "gemini-1.5-pro",
                        "max_tokens": 2000
                    }
                }
            }
        },
        "models": {
            "gpt-4": {
                "config-name": "openai-config",
                "use-in": ["gateway", "api"],
                "type": ["chat"]
            },
            "claude-3": {
                "config-name": "anthropic-config",
                "use-in": ["api"],
                "type": ["chat"]
            },
            "gpt-3.5": {
                "config-name": "fallback-config",
                "use-in": ["test"],
                "type": ["chat"]
            },
            "gemini-pro": {
                "config-name": "gemini-config",
                "use-in": ["gateway"],
                "type": ["chat", "image"]
            },
            "embedding-model": {
                "config-name": "openai-config",
                "use-in": ["api"],
                "type": ["embedding"]
            }
        },
        "modelsmap": {
            "gpt-4-turbo": "gpt-4",
            "claude-3-sonnet": "claude-3",
            "gpt-3.5-turbo": "gpt-3.5",
            "gemini-1.5-pro": "gemini-pro",
            "text-embedding-ada-002": "embedding-model",
            "old-model": "gpt-4",
            "deprecated-claude": "claude-3"
        },
        "providers": {
            "openai-main": {
                "provider": "openai",
                "api_key": "sk-openai"
            },
            "anthropic-1": {
                "provider": "anthropic",
                "api_key": "sk-anthropic-1"
            },
            "anthropic-2": {
                "provider": "anthropic",
                "api_key": "sk-anthropic-2"
            },
            "google-vertex": {
                "provider": "google",
                "api_key": "sk-google"
            }
        }
    };
}

// 分析跳转关系
function analyzeJumpRelationships(config) {
    console.log('\n📊 分析跳转关系...');
    
    const relationships = {
        modelToConfig: {},
        configToModel: {},
        mappingToModel: {},
        modelToMapping: {}
    };
    
    // 分析模型到配置的关系
    Object.entries(config.models || {}).forEach(([modelName, modelConfig]) => {
        const configName = modelConfig['config-name'];
        if (configName) {
            relationships.modelToConfig[modelName] = configName;
            
            if (!relationships.configToModel[configName]) {
                relationships.configToModel[configName] = [];
            }
            relationships.configToModel[configName].push(modelName);
        }
    });
    
    // 分析映射到模型的关系
    Object.entries(config.modelsmap || {}).forEach(([from, to]) => {
        relationships.mappingToModel[from] = to;
        
        if (!relationships.modelToMapping[to]) {
            relationships.modelToMapping[to] = [];
        }
        relationships.modelToMapping[to].push(from);
    });
    
    return relationships;
}

// 验证跳转关系
function validateJumpRelationships(config, relationships) {
    console.log('\n🔍 验证跳转关系...');
    
    const validation = {
        validModelToConfig: 0,
        invalidModelToConfig: 0,
        validMappingToModel: 0,
        invalidMappingToModel: 0,
        orphanConfigs: [],
        orphanModels: [],
        errors: []
    };
    
    // 验证模型到配置的跳转
    Object.entries(relationships.modelToConfig).forEach(([modelName, configName]) => {
        if (config.configurations[configName]) {
            validation.validModelToConfig++;
            console.log(`✅ ${modelName} → ${configName}`);
        } else {
            validation.invalidModelToConfig++;
            validation.errors.push(`模型 ${modelName} 引用了不存在的配置 ${configName}`);
            console.log(`❌ ${modelName} → ${configName} (配置不存在)`);
        }
    });
    
    // 验证映射到模型的跳转
    Object.entries(relationships.mappingToModel).forEach(([from, to]) => {
        if (config.models[to]) {
            validation.validMappingToModel++;
            console.log(`✅ ${from} → ${to}`);
        } else {
            validation.invalidMappingToModel++;
            validation.errors.push(`映射 ${from} 指向了不存在的模型 ${to}`);
            console.log(`❌ ${from} → ${to} (模型不存在)`);
        }
    });
    
    // 查找孤立的配置（没有模型使用）
    Object.keys(config.configurations || {}).forEach(configName => {
        if (!relationships.configToModel[configName]) {
            validation.orphanConfigs.push(configName);
        }
    });
    
    // 查找孤立的模型（没有映射指向）
    Object.keys(config.models || {}).forEach(modelName => {
        if (!relationships.modelToMapping[modelName]) {
            validation.orphanModels.push(modelName);
        }
    });
    
    return validation;
}

// 生成跳转关系报告
function generateJumpReport(config, relationships, validation) {
    console.log('\n📋 跳转关系报告');
    console.log('================');
    
    console.log(`\n🔗 跳转统计:`);
    console.log(`   - 有效模型→配置跳转: ${validation.validModelToConfig}`);
    console.log(`   - 无效模型→配置跳转: ${validation.invalidModelToConfig}`);
    console.log(`   - 有效映射→模型跳转: ${validation.validMappingToModel}`);
    console.log(`   - 无效映射→模型跳转: ${validation.invalidMappingToModel}`);
    
    console.log(`\n📊 关系统计:`);
    console.log(`   - 配置数量: ${Object.keys(config.configurations || {}).length}`);
    console.log(`   - 模型数量: ${Object.keys(config.models || {}).length}`);
    console.log(`   - 映射数量: ${Object.keys(config.modelsmap || {}).length}`);
    console.log(`   - 孤立配置: ${validation.orphanConfigs.length}`);
    console.log(`   - 孤立模型: ${validation.orphanModels.length}`);
    
    if (validation.orphanConfigs.length > 0) {
        console.log(`\n🔸 孤立配置 (无模型使用):`);
        validation.orphanConfigs.forEach(config => {
            console.log(`   - ${config}`);
        });
    }
    
    if (validation.orphanModels.length > 0) {
        console.log(`\n🔸 孤立模型 (无映射指向):`);
        validation.orphanModels.forEach(model => {
            console.log(`   - ${model}`);
        });
    }
    
    if (validation.errors.length > 0) {
        console.log(`\n❌ 发现错误:`);
        validation.errors.forEach(error => {
            console.log(`   - ${error}`);
        });
    } else {
        console.log(`\n✅ 所有跳转关系有效`);
    }
    
    return {
        totalJumps: validation.validModelToConfig + validation.validMappingToModel,
        validJumps: validation.validModelToConfig + validation.validMappingToModel,
        invalidJumps: validation.invalidModelToConfig + validation.invalidMappingToModel,
        orphanItems: validation.orphanConfigs.length + validation.orphanModels.length
    };
}

// 测试跳转功能场景
function testJumpScenarios() {
    console.log('\n🎯 测试跳转功能场景...');
    
    const scenarios = [
        {
            name: "模型编辑中的配置跳转",
            description: "在编辑模型时跳转到关联的配置",
            steps: [
                "1. 打开模型编辑对话框",
                "2. 选择配置名称",
                "3. 点击跳转按钮",
                "4. 验证跳转到配置页面并高亮"
            ]
        },
        {
            name: "模型列表中的配置链接",
            description: "在模型列表中点击配置名称跳转",
            steps: [
                "1. 查看模型列表",
                "2. 点击配置名称链接",
                "3. 验证跳转到配置页面"
            ]
        },
        {
            name: "模型映射中的模型跳转",
            description: "在映射列表中跳转到目标模型",
            steps: [
                "1. 查看模型映射列表",
                "2. 点击模型跳转按钮",
                "3. 验证跳转到模型页面并高亮"
            ]
        },
        {
            name: "配置预览功能",
            description: "在模型编辑时预览配置信息",
            steps: [
                "1. 打开模型编辑对话框",
                "2. 选择不同的配置",
                "3. 查看配置预览信息",
                "4. 验证预览内容正确"
            ]
        }
    ];
    
    scenarios.forEach((scenario, index) => {
        console.log(`\n${index + 1}. ${scenario.name}`);
        console.log(`   描述: ${scenario.description}`);
        console.log(`   步骤:`);
        scenario.steps.forEach(step => {
            console.log(`      ${step}`);
        });
    });
    
    return scenarios;
}

// 主测试函数
async function runJumpTests() {
    try {
        console.log('1. 创建跳转测试配置...');
        const testConfig = createJumpTestConfig();
        
        console.log('2. 分析跳转关系...');
        const relationships = analyzeJumpRelationships(testConfig);
        
        console.log('3. 验证跳转关系...');
        const validation = validateJumpRelationships(testConfig, relationships);
        
        console.log('4. 生成跳转报告...');
        const report = generateJumpReport(testConfig, relationships, validation);
        
        console.log('5. 测试跳转场景...');
        const scenarios = testJumpScenarios();
        
        console.log('6. 保存测试配置...');
        fs.writeFileSync('./test-jump-config.json', JSON.stringify(testConfig, null, 2));
        console.log('✅ 跳转测试配置已保存到 test-jump-config.json');
        
        console.log('\n🎉 跳转功能测试完成！');
        console.log('\n📊 测试总结:');
        console.log(`   - 总跳转关系: ${report.totalJumps}`);
        console.log(`   - 有效跳转: ${report.validJumps}`);
        console.log(`   - 无效跳转: ${report.invalidJumps}`);
        console.log(`   - 孤立项目: ${report.orphanItems}`);
        console.log(`   - 测试场景: ${scenarios.length} 个`);
        
        console.log('\n🚀 使用建议:');
        console.log('1. 启动编辑器: npm start');
        console.log('2. 登录系统: http://localhost:3474/login.html');
        console.log('3. 测试模型编辑中的配置跳转功能');
        console.log('4. 测试模型列表中的配置链接');
        console.log('5. 测试模型映射中的模型跳转');
        console.log('6. 验证配置预览功能');
        
        console.log('\n💡 功能特色:');
        console.log('- ✅ 智能跳转导航');
        console.log('- ✅ 配置预览功能');
        console.log('- ✅ 高亮定位效果');
        console.log('- ✅ 关联关系验证');
        console.log('- ✅ 响应式界面设计');
        
    } catch (error) {
        console.log('❌ 跳转功能测试失败:', error.message);
        console.log('\n🔧 建议:');
        console.log('1. 检查配置文件格式是否正确');
        console.log('2. 确保有文件写入权限');
        console.log('3. 查看详细错误信息进行调试');
    }
}

// 运行测试
runJumpTests();
