# 网关路径配置说明

## 概述

现在支持配置任意的网关路径，不再限制于固定的 `/gateway/*` 路径。您可以设置任意数字或字母的组合作为路径，并且区分大小写。

## 配置方法

### 1. 配置网关路径

在 `conf.json` 文件中添加 `gateway_paths` 配置：

```json
{
  "gateway_paths": [
    "gateway",
    "test",
    "api",
    "v1"
  ]
}
```

### 2. 配置模型的使用范围

为每个模型添加 `use-in` 参数，指定该模型在哪些路径下可用：

```json
{
  "models": {
    "gpt-4o": {
      "config-name": "common-config-2",
      "use-in": [
        "gateway",
        "api"
      ],
      "type": [
        "chat"
      ]
    },
    "claude-3-5-sonnet": {
      "config-name": "common-config-6",
      "use-in": [
        "test"
      ],
      "type": [
        "chat"
      ]
    }
  }
}
```

## 功能说明

### 1. 普通请求处理

对于非 `/v1/models` 的请求，不同路径的处理方式完全一致：

- `/gateway/v1/chat/completions` 
- `/test/v1/chat/completions`
- `/api/v1/chat/completions`

这些请求的处理逻辑完全相同，只是路径不同。

### 2. 模型列表请求处理

对于 `/v1/models` 请求，会根据路径和模型的 `use-in` 配置进行过滤：

#### 示例：

假设有以下配置：
```json
{
  "gateway_paths": ["gateway", "test"],
  "models": {
    "gpt-4o": {
      "use-in": ["gateway"],
      "type": ["chat"]
    },
    "claude-3-5-sonnet": {
      "use-in": ["test"],
      "type": ["chat"]
    },
    "gpt-4o-mini": {
      "use-in": ["gateway", "test"],
      "type": ["chat"]
    }
  }
}
```

请求结果：
- `GET /gateway/v1/models` → 返回 `gpt-4o` 和 `gpt-4o-mini`
- `GET /test/v1/models` → 返回 `claude-3-5-sonnet` 和 `gpt-4o-mini`

### 3. 过滤规则

模型在 `/v1/models` 中显示需要同时满足：
1. `use-in` 参数包含当前请求的路径
2. `type` 参数包含 "chat"（只显示聊天模型）

## 使用示例

### 配置多个环境

```json
{
  "gateway_paths": [
    "prod",
    "dev", 
    "test"
  ],
  "models": {
    "gpt-4o": {
      "use-in": ["prod"],
      "type": ["chat"]
    },
    "gpt-4o-mini": {
      "use-in": ["dev", "test"],
      "type": ["chat"]
    }
  }
}
```

### 配置不同的API版本

```json
{
  "gateway_paths": [
    "v1",
    "v2",
    "beta"
  ],
  "models": {
    "stable-model": {
      "use-in": ["v1", "v2"],
      "type": ["chat"]
    },
    "experimental-model": {
      "use-in": ["beta"],
      "type": ["chat"]
    }
  }
}
```

## 注意事项

1. 路径名称区分大小写
2. 如果模型没有 `use-in` 参数，默认为 `["gateway"]`
3. 如果 `gateway_paths` 未配置，默认为 `["gateway"]`
4. 只有 `type` 包含 "chat" 的模型才会在 `/v1/models` 中显示
5. 服务器启动时会自动为所有配置的路径注册路由

## 迁移指南

现有的配置无需修改，系统会自动：
1. 为所有模型添加默认的 `use-in: ["gateway"]`
2. 使用默认的 `gateway_paths: ["gateway"]`

这确保了向后兼容性。
