/* Gateway Config Editor Styles */
.gw-config-editor * {
    box-sizing: border-box;
}

.gw-config-editor {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #f8fafc;
    min-height: 100vh;
    color: #334155;
    line-height: 1.6;
}

/* 头部样式 */
.gw-header {
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.gw-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.gw-header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gw-header-actions {
    display: flex;
    gap: 0.75rem;
}

/* 主要布局 */
.gw-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
}

/* 侧边栏 */
.gw-sidebar {
    background: #ffffff;
    border-radius: 0.75rem;
    padding: 1.5rem 0;
    height: fit-content;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.gw-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.gw-nav-item {
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    color: #64748b;
}

.gw-nav-item:hover {
    background: #f1f5f9;
    color: #3b82f6;
}

.gw-nav-item.active {
    background: #eff6ff;
    color: #3b82f6;
    border-right: 3px solid #3b82f6;
}

/* 内容区域 */
.gw-content {
    background: #ffffff;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.gw-section {
    display: none;
    padding: 2rem;
}

.gw-section.active {
    display: block;
}

.gw-section-header {
    margin-bottom: 2rem;
}

.gw-section-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: #1e293b;
}

.gw-section-desc {
    margin: 0;
    color: #64748b;
    font-size: 0.95rem;
}

/* 卡片样式 */
.gw-card {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.gw-card-header {
    background: #f8fafc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.gw-card-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
}

.gw-card-body {
    padding: 1.5rem;
}

/* 按钮样式 */
.gw-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;
}

.gw-btn-primary {
    background: #3b82f6;
    color: white;
}

.gw-btn-primary:hover {
    background: #2563eb;
}

.gw-btn-secondary {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.gw-btn-secondary:hover {
    background: #e2e8f0;
}

.gw-btn-danger {
    background: #ef4444;
    color: white;
}

.gw-btn-danger:hover {
    background: #dc2626;
}

.gw-btn-small {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

/* 搜索框 */
.gw-search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.gw-search-box input {
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    width: 200px;
    transition: border-color 0.2s ease;
}

.gw-search-box input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.gw-search-box i {
    position: absolute;
    right: 0.75rem;
    color: #9ca3af;
}

/* 路径项样式 */
.gw-path-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
}

.gw-path-item input {
    flex: 1;
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.gw-path-item input:focus {
    outline: none;
    border-color: #3b82f6;
}

/* 配置项样式 */
.gw-config-item {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.gw-config-header {
    background: #f8fafc;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.gw-config-header:hover {
    background: #f1f5f9;
}

.gw-config-title {
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gw-config-body {
    padding: 1rem;
    border-top: 1px solid #e2e8f0;
    display: none;
}

.gw-config-body.expanded {
    display: block;
}

/* 表单样式 */
.gw-form-group {
    margin-bottom: 1rem;
}

.gw-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.gw-form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.gw-form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.gw-form-select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background: white;
    cursor: pointer;
}

/* 标签选择器 */
.gw-tag-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.gw-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: #eff6ff;
    color: #3b82f6;
    border: 1px solid #bfdbfe;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.gw-tag.selected {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.gw-tag:hover {
    background: #dbeafe;
}

.gw-tag.selected:hover {
    background: #2563eb;
}

/* 拖拽排序 */
.gw-sortable {
    min-height: 2rem;
}

.gw-sortable-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: move;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.gw-sortable-item:hover {
    background: #f1f5f9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.gw-sortable-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.gw-drag-handle {
    color: #9ca3af;
    cursor: grab;
}

.gw-drag-handle:active {
    cursor: grabbing;
}

/* 模态框样式 */
.gw-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.gw-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: gw-fadeIn 0.3s ease;
}

.gw-modal-content {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: gw-slideUp 0.3s ease;
}

.gw-modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.gw-modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.gw-modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: color 0.2s ease;
}

.gw-modal-close:hover {
    color: #6b7280;
}

.gw-modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.gw-modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 加载指示器 */
.gw-loading {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 2000;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
}

.gw-loading.show {
    display: flex;
}

.gw-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: gw-spin 1s linear infinite;
}

/* 通知样式 */
.gw-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1500;
}

.gw-notification.show {
    transform: translateX(0);
}

.gw-notification.error {
    background: #ef4444;
}

.gw-notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 动画 */
@keyframes gw-fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes gw-slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gw-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模型项和提供商项样式 */
.gw-model-item, .gw-provider-item {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.gw-model-item:hover, .gw-provider-item:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.gw-item-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.gw-item-title {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.95rem;
}

.gw-item-subtitle {
    font-size: 0.8rem;
    color: #64748b;
    margin-top: 0.25rem;
}

.gw-item-actions {
    display: flex;
    gap: 0.5rem;
}

.gw-item-body {
    padding: 0 1rem 1rem 1rem;
    border-top: 1px solid #f1f5f9;
    display: none;
}

.gw-item-body.expanded {
    display: block;
}

/* 映射项样式 */
.gw-mapping-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
}

.gw-mapping-item input {
    flex: 1;
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.gw-mapping-arrow {
    color: #9ca3af;
    font-weight: bold;
}

/* 嵌套目标样式 */
.gw-nested-targets {
    border-left: 2px solid #e2e8f0;
    padding-left: 1rem;
    margin: 0.5rem 0;
}

.gw-target-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.gw-target-item:hover {
    background: #f1f5f9;
    border-color: #3b82f6;
}

.gw-target-simple {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
}

.gw-target-complex {
    overflow: hidden;
}

.gw-target-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    cursor: pointer;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.gw-target-header:hover {
    background: #f1f5f9;
}

.gw-target-body {
    padding: 1rem;
    background: white;
    border-top: 1px solid #f1f5f9;
}

.gw-target-icon {
    color: #6b7280;
    width: 16px;
    text-align: center;
}

.gw-target-name {
    flex: 1;
    font-weight: 500;
    color: #374151;
}

.gw-target-actions {
    display: flex;
    gap: 0.5rem;
}

.gw-target-simple .gw-target-name {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f1f5f9;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.85rem;
}

/* JSON显示样式 */
.gw-json-display {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
    color: #374151;
    overflow-x: auto;
    white-space: pre-wrap;
    margin: 0;
}

/* 拖拽状态 */
.gw-target-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    z-index: 1000;
}

/* 嵌套层级指示 */
.gw-nested-targets[style*="margin-left: 20px"] {
    border-left-color: #3b82f6;
}

.gw-nested-targets[style*="margin-left: 40px"] {
    border-left-color: #10b981;
}

.gw-nested-targets[style*="margin-left: 60px"] {
    border-left-color: #f59e0b;
}

/* 添加目标容器 */
.gw-add-target-container {
    padding: 0.75rem;
    text-align: center;
    border-bottom: 1px dashed #d1d5db;
    margin-bottom: 0.5rem;
}

/* 目标名称输入框 */
.gw-target-name-input {
    flex: 1;
    background: transparent;
    border: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    color: #374151;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.gw-target-name-input:focus,
.gw-target-name-input.editing {
    background: #f1f5f9;
    border: 1px solid #3b82f6;
    outline: none;
}

/* JSON编辑器 */
.gw-json-editor {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
    color: #374151;
    background: #f8fafc;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.gw-json-editor:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 复选框组 */
.gw-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.gw-checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
    padding: 0.375rem 0;
}

.gw-checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
    cursor: pointer;
}

.gw-checkbox-label:hover {
    color: #1f2937;
}

/* 表单区域动画 */
.gw-form-section {
    transition: all 0.3s ease;
    overflow: hidden;
}

.gw-form-section[style*="display: none"] {
    opacity: 0;
    max-height: 0;
}

.gw-form-section[style*="display: block"] {
    opacity: 1;
    max-height: 1000px;
}

/* 目标项动画 */
.gw-target-item {
    transition: all 0.2s ease;
}

.gw-target-item:hover .gw-target-actions {
    opacity: 1;
}

.gw-target-actions {
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

/* 复杂目标展开动画 */
.gw-target-body {
    transition: all 0.3s ease;
    overflow: hidden;
}

.gw-target-header .fas.fa-chevron-down {
    transition: transform 0.2s ease;
}

.gw-target-header .fas.fa-chevron-up {
    transform: rotate(180deg);
}

/* 拖拽提示 */
.gw-drag-handle {
    opacity: 0.3;
    transition: opacity 0.2s ease;
}

.gw-target-item:hover .gw-drag-handle {
    opacity: 0.7;
}

.gw-target-item[draggable="true"]:hover .gw-drag-handle {
    opacity: 1;
    color: #3b82f6;
}

/* 参数编辑器样式 */
.gw-params-editor {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background: #f8fafc;
    padding: 1rem;
    margin-bottom: 0.75rem;
}

.gw-params-section {
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background: white;
    overflow: hidden;
}

.gw-params-title {
    background: #f1f5f9;
    padding: 0.75rem 1rem;
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

.gw-params-title i {
    margin-right: 0.5rem;
    color: #6b7280;
}

.gw-params-fields {
    padding: 1rem;
}

.gw-param-field {
    display: grid;
    grid-template-columns: 1fr 2fr auto;
    gap: 0.75rem;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
}

.gw-param-key,
.gw-param-value {
    display: flex;
    flex-direction: column;
}

.gw-param-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    transition: border-color 0.2s ease;
}

.gw-param-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.gw-param-actions {
    display: flex;
    gap: 0.25rem;
}

.gw-params-list {
    padding: 1rem;
}

.gw-param-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 0.25rem;
    border: 1px solid #e2e8f0;
}

.gw-param-item .gw-param-input {
    flex: 1;
}

.gw-params-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid #e2e8f0;
}

.gw-params-help {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
    font-size: 0.875rem;
}

.gw-params-help h5 {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #0369a1;
}

.gw-params-help ul {
    margin: 0 0 1rem 1.5rem;
    padding: 0;
}

.gw-params-help li {
    margin-bottom: 0.25rem;
    color: #374151;
}

.gw-params-help code {
    background: #e0f2fe;
    color: #0c4a6e;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
}

.gw-no-params {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 2rem;
    margin: 0;
}

.gw-btn-tiny {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1;
}

.gw-form-help {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.25rem;
    display: block;
}

/* 参数编辑器动画 */
.gw-params-section {
    transition: all 0.2s ease;
}

.gw-params-section:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.gw-param-field:hover {
    background: #f1f5f9;
    border-color: #3b82f6;
}

/* 错误状态 */
.gw-json-editor.error {
    border-color: #ef4444;
    background: #fef2f2;
}

.gw-target-name-input.error {
    border-color: #ef4444;
    background: #fef2f2;
}

.gw-param-input.error {
    border-color: #ef4444;
    background: #fef2f2;
}

/* 配置参数编辑器样式 */
.gw-config-params-editor {
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background: #f8fafc;
    padding: 1rem;
    margin-bottom: 0.75rem;
    min-height: 60px;
}

.gw-config-params-section {
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    background: white;
    overflow: hidden;
}

.gw-config-params-section:last-child {
    margin-bottom: 0;
}

.gw-config-params-title {
    background: #f1f5f9;
    padding: 0.5rem 0.75rem;
    margin: 0;
    font-size: 0.85rem;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e2e8f0;
}

.gw-config-params-title i {
    margin-right: 0.5rem;
    color: #6b7280;
}

.gw-config-params-fields {
    padding: 0.75rem;
}

.gw-config-param-field {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 0.375rem 0.5rem;
    background: #f8fafc;
    border-radius: 0.25rem;
    border: 1px solid #e2e8f0;
}

.gw-config-param-field:last-child {
    margin-bottom: 0;
}

.gw-config-param-key {
    font-weight: 500;
    color: #374151;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
    min-width: 80px;
}

.gw-config-param-value {
    flex: 1;
    color: #6b7280;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
    word-break: break-all;
}

.gw-config-params-list {
    padding: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.gw-config-param-tag {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    border: 1px solid #bfdbfe;
}

.gw-no-params {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 1rem;
    margin: 0;
    font-size: 0.875rem;
}

/* 配置参数编辑器动画 */
.gw-config-params-section {
    transition: all 0.2s ease;
}

.gw-config-params-section:hover {
    border-color: #3b82f6;
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1);
}

.gw-config-param-field:hover {
    background: #f1f5f9;
    border-color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .gw-param-field {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .gw-params-actions {
        flex-direction: column;
    }

    .gw-params-help {
        font-size: 0.8rem;
    }

    .gw-config-param-field {
        flex-direction: column;
        align-items: stretch;
        gap: 0.25rem;
    }

    .gw-config-param-key {
        min-width: auto;
    }

    .gw-config-params-list {
        flex-direction: column;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .gw-main {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .gw-sidebar {
        order: 2;
    }

    .gw-content {
        order: 1;
    }

    .gw-section {
        padding: 1rem;
    }

    .gw-card-header {
        flex-direction: column;
        align-items: stretch;
    }

    .gw-search-box input {
        width: 100%;
    }

    .gw-header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .gw-header-actions {
        justify-content: center;
    }

    .gw-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .gw-notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
    }

    .gw-mapping-item {
        flex-direction: column;
        align-items: stretch;
    }

    .gw-mapping-arrow {
        align-self: center;
        transform: rotate(90deg);
    }

    .gw-config-selector {
        flex-direction: column;
        gap: 0.5rem;
    }

    .gw-mapping-field {
        width: 100%;
    }
}

/* 跳转功能样式 */
.gw-config-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gw-config-selector .gw-form-select {
    flex: 1;
}

.gw-jump-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    white-space: nowrap;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.gw-jump-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.gw-jump-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.gw-jump-btn:hover::before {
    left: 100%;
}

.gw-config-preview {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.gw-config-preview-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.gw-preview-type {
    font-weight: 600;
    color: #374151;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.gw-preview-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.gw-preview-label {
    font-weight: 500;
    color: #6b7280;
    min-width: 80px;
}

.gw-preview-value {
    color: #374151;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
}

.gw-preview-empty {
    color: #9ca3af;
    font-style: italic;
    text-align: center;
    margin: 0;
}

.gw-config-link {
    color: #3b82f6;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.gw-config-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.gw-config-link i {
    font-size: 0.75rem;
    opacity: 0.7;
}

.gw-mapping-field {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex: 1;
}

.gw-mapping-field input {
    flex: 1;
}

.gw-mapping-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
}

.gw-mapping-arrow {
    color: #6b7280;
    font-weight: 600;
    font-size: 1.1rem;
}

/* 高亮效果 */
.gw-highlight {
    animation: highlight 3s ease-in-out;
    border: 2px solid #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

@keyframes highlight {
    0% {
        background-color: rgba(59, 130, 246, 0.1);
    }
    50% {
        background-color: rgba(59, 130, 246, 0.2);
    }
    100% {
        background-color: transparent;
    }
}
