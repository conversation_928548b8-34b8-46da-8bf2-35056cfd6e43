// 测试多层嵌套编辑功能
const fs = require('fs');

console.log('🧪 测试多层嵌套编辑功能');
console.log('================================');

// 创建测试配置
function createTestConfig() {
    return {
        "gateway_paths": ["gateway", "test", "api"],
        "configurations": {
            "simple-config": "provider1",
            "loadbalance-config": {
                "strategy": { "mode": "loadbalance" },
                "targets": ["provider1", "provider2", "provider3"]
            },
            "fallback-config": {
                "strategy": { "mode": "fallback" },
                "targets": ["provider1", "provider2"]
            },
            "complex-nested": {
                "strategy": { "mode": "fallback" },
                "targets": [
                    {
                        "strategy": { "mode": "loadbalance" },
                        "targets": ["fast1", "fast2", "fast3"]
                    },
                    {
                        "strategy": { "mode": "fallback" },
                        "targets": [
                            {
                                "strategy": { "mode": "loadbalance" },
                                "targets": ["backup1", "backup2"]
                            },
                            "final-backup"
                        ]
                    },
                    {
                        "base_provider": "gemini",
                        "added_params": {
                            "override_params": {
                                "model": "gemini-1.5-flash"
                            }
                        }
                    }
                ]
            }
        },
        "models": {
            "gpt-4o": {
                "config-name": "complex-nested",
                "use-in": ["gateway", "api"],
                "type": ["chat"]
            },
            "gpt-4o-mini": {
                "config-name": "fallback-config",
                "use-in": ["test"],
                "type": ["chat"]
            }
        },
        "modelsmap": {
            "gpt-4": "gpt-4o"
        },
        "providers": {
            "provider1": {
                "provider": "openai",
                "api_key": "sk-test1"
            },
            "provider2": {
                "provider": "anthropic",
                "api_key": "sk-test2"
            },
            "fast1": {
                "provider": "openai",
                "api_key": "sk-fast1"
            },
            "backup1": {
                "provider": "azure-openai",
                "api_key": "sk-backup1"
            },
            "gemini": {
                "provider": "google",
                "api_key": "sk-gemini"
            }
        }
    };
}

// 验证配置结构
function validateConfig(config) {
    const errors = [];
    const warnings = [];
    
    console.log('\n📋 验证配置结构...');
    
    // 检查基本结构
    if (!config.configurations) {
        errors.push('缺少 configurations 部分');
    }
    
    if (!config.models) {
        errors.push('缺少 models 部分');
    }
    
    if (!config.providers) {
        errors.push('缺少 providers 部分');
    }
    
    // 检查嵌套配置
    if (config.configurations) {
        Object.entries(config.configurations).forEach(([name, conf]) => {
            if (typeof conf === 'object' && conf.targets) {
                const result = validateNestedTargets(conf.targets, name, 0);
                errors.push(...result.errors);
                warnings.push(...result.warnings);
            }
        });
    }
    
    return { errors, warnings };
}

// 验证嵌套目标
function validateNestedTargets(targets, configName, level) {
    const errors = [];
    const warnings = [];
    
    if (!Array.isArray(targets)) {
        errors.push(`配置 ${configName} 的 targets 不是数组`);
        return { errors, warnings };
    }
    
    targets.forEach((target, index) => {
        if (typeof target === 'string') {
            console.log(`  ${'  '.repeat(level)}✅ 简单目标: ${target}`);
        } else if (typeof target === 'object') {
            console.log(`  ${'  '.repeat(level)}🔧 复杂目标 #${index}:`);
            
            if (target.strategy) {
                console.log(`  ${'  '.repeat(level + 1)}📋 策略: ${target.strategy.mode}`);
                if (!['loadbalance', 'fallback'].includes(target.strategy.mode)) {
                    errors.push(`无效的策略模式: ${target.strategy.mode}`);
                }
            }
            
            if (target.base_provider) {
                console.log(`  ${'  '.repeat(level + 1)}🖥️  基础提供商: ${target.base_provider}`);
            }
            
            if (target.added_params) {
                console.log(`  ${'  '.repeat(level + 1)}⚙️  附加参数: ${Object.keys(target.added_params).join(', ')}`);
            }
            
            if (target.targets) {
                const result = validateNestedTargets(target.targets, configName, level + 1);
                errors.push(...result.errors);
                warnings.push(...result.warnings);
            }
        }
    });
    
    return { errors, warnings };
}

// 测试配置操作
function testConfigOperations() {
    console.log('\n🔧 测试配置操作...');
    
    const config = createTestConfig();
    
    // 测试1: 添加简单目标
    console.log('测试1: 添加简单目标');
    config.configurations['fallback-config'].targets.push('new-provider');
    console.log('✅ 成功添加简单目标');
    
    // 测试2: 添加复杂目标
    console.log('测试2: 添加复杂目标');
    config.configurations['fallback-config'].targets.push({
        "strategy": { "mode": "loadbalance" },
        "targets": ["new1", "new2"]
    });
    console.log('✅ 成功添加复杂目标');
    
    // 测试3: 修改嵌套目标
    console.log('测试3: 修改嵌套目标');
    const complexConfig = config.configurations['complex-nested'];
    if (complexConfig.targets[0] && complexConfig.targets[0].targets) {
        complexConfig.targets[0].targets[0] = 'modified-fast1';
        console.log('✅ 成功修改嵌套目标');
    }
    
    // 测试4: 删除目标
    console.log('测试4: 删除目标');
    config.configurations['fallback-config'].targets.splice(0, 1);
    console.log('✅ 成功删除目标');
    
    return config;
}

// 主测试函数
async function runTests() {
    try {
        console.log('1. 创建测试配置...');
        const testConfig = createTestConfig();
        
        console.log('2. 验证原始配置...');
        const validation = validateConfig(testConfig);
        
        if (validation.errors.length > 0) {
            console.log('❌ 配置验证失败:');
            validation.errors.forEach(error => console.log(`   - ${error}`));
        } else {
            console.log('✅ 配置验证通过');
        }
        
        if (validation.warnings.length > 0) {
            console.log('⚠️  警告:');
            validation.warnings.forEach(warning => console.log(`   - ${warning}`));
        }
        
        console.log('3. 测试配置操作...');
        const modifiedConfig = testConfigOperations();
        
        console.log('4. 验证修改后的配置...');
        const modifiedValidation = validateConfig(modifiedConfig);
        
        if (modifiedValidation.errors.length > 0) {
            console.log('❌ 修改后配置验证失败:');
            modifiedValidation.errors.forEach(error => console.log(`   - ${error}`));
        } else {
            console.log('✅ 修改后配置验证通过');
        }
        
        console.log('5. 保存测试配置...');
        fs.writeFileSync('./test-nested-config.json', JSON.stringify(modifiedConfig, null, 2));
        console.log('✅ 测试配置已保存到 test-nested-config.json');
        
        console.log('\n🎉 所有测试完成！');
        console.log('\n📋 测试总结:');
        console.log(`- 配置项数量: ${Object.keys(testConfig.configurations).length}`);
        console.log(`- 模型数量: ${Object.keys(testConfig.models).length}`);
        console.log(`- 提供商数量: ${Object.keys(testConfig.providers).length}`);
        console.log(`- 网关路径: ${testConfig.gateway_paths.join(', ')}`);
        
        console.log('\n🚀 使用建议:');
        console.log('1. 启动编辑器: npm start');
        console.log('2. 登录系统: http://localhost:3474/login.html');
        console.log('3. 查看"配置策略"部分的复杂嵌套结构');
        console.log('4. 尝试编辑、添加、删除操作');
        console.log('5. 测试fallback模式的拖拽排序功能');
        
    } catch (error) {
        console.log('❌ 测试失败:', error.message);
        console.log('\n🔧 建议:');
        console.log('1. 检查Node.js版本是否支持');
        console.log('2. 确保有文件写入权限');
        console.log('3. 查看详细错误信息进行调试');
    }
}

// 运行测试
runTests();
